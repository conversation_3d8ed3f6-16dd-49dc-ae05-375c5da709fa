/**
 * 促销活动API路由
 * 
 * 功能说明：
 * 1. 处理促销活动的CRUD操作
 * 2. 支持复杂的分页查询和筛选
 * 3. 提供数据验证和错误处理
 * 4. 实现RESTful API规范
 */

import { NextRequest } from 'next/server';
import { promotionDAO, competitorDAO, ticketTypeDAO } from '@/lib/dao';
import {
  createSuccessResponse,
  createValidationErrorResponse,
  parsePaginationParams,
  parseQueryParams,
  parseRequestBody,
  validateRequiredFields,
  validateStringLength,
  validateNumberRange,
  validateDateFormat,
  validateUrlFormat,
  withErrorHandling,
  createMethodNotAllowedResponse
} from '@/lib/api-utils';
import { CreatePromotionInput, PromotionFilters } from '@/types';

/**
 * GET /api/promotions
 * 获取促销活动列表（支持分页和筛选）
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('📥 GET /api/promotions - 获取促销活动列表');

  // 解析分页参数
  const paginationParams = parsePaginationParams(request);
  console.log('📄 分页参数:', paginationParams);

  // 解析筛选参数
  const queryParams = parseQueryParams(request);
  const filters: PromotionFilters = {
    competitor_id: queryParams.competitor_id,
    ticket_type_id: queryParams.ticket_type_id,
    activity_name: queryParams.activity_name,
    sales_channel: queryParams.sales_channel,
    sale_date_start: queryParams.sale_date_start,
    sale_date_end: queryParams.sale_date_end,
    use_date_start: queryParams.use_date_start,
    use_date_end: queryParams.use_date_end,
    min_price: queryParams.min_price,
    max_price: queryParams.max_price
  };
  console.log('🔍 筛选条件:', filters);

  // 查询数据
  const result = await promotionDAO.findWithFilters(paginationParams, filters);

  console.log(`✅ 成功获取促销活动列表，共${result.total}条记录`);
  return createSuccessResponse(result, '获取促销活动列表成功');
});

/**
 * POST /api/promotions
 * 创建新促销活动
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  console.log('📥 POST /api/promotions - 创建新促销活动');

  // 解析请求体
  const body = await parseRequestBody<CreatePromotionInput>(request);
  console.log('📝 创建数据:', body);

  // 验证必需字段
  const { isValid, missingFields } = validateRequiredFields(body, [
    'competitor_id',
    'ticket_type_id',
    'activity_name'
  ]);
  if (!isValid) {
    console.log('❌ 缺少必需字段:', missingFields);
    return createValidationErrorResponse(
      `缺少必需字段: ${missingFields.join(', ')}`,
      { missingFields }
    );
  }

  // 验证外键关联
  const competitor = await competitorDAO.findById(body.competitor_id);
  if (!competitor) {
    return createValidationErrorResponse('指定的竞品不存在');
  }

  const ticketType = await ticketTypeDAO.findById(body.ticket_type_id);
  if (!ticketType) {
    return createValidationErrorResponse('指定的票种不存在');
  }

  // 验证字段格式和长度
  if (!validateStringLength(body.activity_name, 1, 255)) {
    return createValidationErrorResponse('活动名称长度必须在1-255个字符之间');
  }

  if (body.rack_rate !== undefined && !validateNumberRange(body.rack_rate, 0, 999999.99)) {
    return createValidationErrorResponse('门市价必须在0-999999.99之间');
  }

  if (body.promo_price !== undefined && !validateNumberRange(body.promo_price, 0, 999999.99)) {
    return createValidationErrorResponse('促销价必须在0-999999.99之间');
  }

  // 验证日期格式
  if (body.sale_start_date && !validateDateFormat(body.sale_start_date)) {
    return createValidationErrorResponse('销售开始日期格式无效');
  }

  if (body.sale_end_date && !validateDateFormat(body.sale_end_date)) {
    return createValidationErrorResponse('销售结束日期格式无效');
  }

  if (body.use_start_date && !validateDateFormat(body.use_start_date)) {
    return createValidationErrorResponse('使用开始日期格式无效');
  }

  if (body.use_end_date && !validateDateFormat(body.use_end_date)) {
    return createValidationErrorResponse('使用结束日期格式无效');
  }

  // 验证日期逻辑
  if (body.sale_start_date && body.sale_end_date) {
    if (new Date(body.sale_start_date) > new Date(body.sale_end_date)) {
      return createValidationErrorResponse('销售开始日期不能晚于结束日期');
    }
  }

  if (body.use_start_date && body.use_end_date) {
    if (new Date(body.use_start_date) > new Date(body.use_end_date)) {
      return createValidationErrorResponse('使用开始日期不能晚于结束日期');
    }
  }

  // 验证其他字段
  if (body.sales_channel && !validateStringLength(body.sales_channel, 0, 255)) {
    return createValidationErrorResponse('销售渠道长度不能超过255个字符');
  }

  if (body.data_source_url && !validateUrlFormat(body.data_source_url)) {
    return createValidationErrorResponse('数据来源URL格式无效');
  }

  if (body.data_source_url && !validateStringLength(body.data_source_url, 0, 512)) {
    return createValidationErrorResponse('数据来源URL长度不能超过512个字符');
  }

  // 创建促销活动
  const promotionId = await promotionDAO.create(body);

  // 获取创建的促销活动详情
  const newPromotion = await promotionDAO.findDetailById(promotionId);

  console.log(`✅ 成功创建促销活动，ID: ${promotionId}`);
  return createSuccessResponse(newPromotion, '创建促销活动成功');
});

/**
 * 处理不支持的HTTP方法
 */
export async function PUT(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'POST']);
}

export async function DELETE(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'POST']);
}

export async function PATCH(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'POST']);
}
