/**
 * 促销活动批量导入API
 * 
 * 功能说明：
 * 1. 接收Excel文件上传
 * 2. 解析Excel文件内容
 * 3. 验证数据格式和业务规则
 * 4. 批量创建促销活动记录
 * 5. 返回导入结果和错误详情
 */

import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import { promotionDAO } from '@/lib/dao';
import { BulkImportPromotionInput, BulkImportResult } from '@/types';

// ============================================================================
// 常量定义
// ============================================================================

/**
 * 文件上传限制
 */
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_ROWS = 1000; // 最大导入行数
const ALLOWED_EXTENSIONS = ['.xlsx', '.xls'];

/**
 * Excel列映射配置
 */
const COLUMN_MAPPING = {
  '竞品名称': 'competitor_name',
  '票种名称': 'ticket_type_name',
  '活动名称': 'activity_name',
  '门市价': 'rack_rate',
  '促销价': 'promo_price',
  '销售开始日期': 'sale_start_date',
  '销售结束日期': 'sale_end_date',
  '使用开始日期': 'use_start_date',
  '使用结束日期': 'use_end_date',
  '销售渠道': 'sales_channel',
  '使用规则': 'usage_rules',
  '数据来源URL': 'data_source_url',
  '备注': 'remarks'
};

// ============================================================================
// API处理函数
// ============================================================================

/**
 * POST请求处理 - 批量导入促销活动
 */
export async function POST(request: NextRequest) {
  try {
    console.log('📥 开始处理促销活动批量导入请求');

    // 解析表单数据
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'NO_FILE_PROVIDED',
          message: '请选择要上传的Excel文件'
        }
      }, { status: 400 });
    }

    // 验证文件
    const validationResult = validateFile(file);
    if (!validationResult.valid) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'FILE_VALIDATION_FAILED',
          message: validationResult.message
        }
      }, { status: 400 });
    }

    // 读取文件内容
    const fileBuffer = await file.arrayBuffer();
    const workbook = XLSX.read(fileBuffer, { type: 'buffer' });

    // 获取第一个工作表
    const firstSheetName = workbook.SheetNames[0];
    if (!firstSheetName) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'NO_WORKSHEET_FOUND',
          message: 'Excel文件中没有找到工作表'
        }
      }, { status: 400 });
    }

    const worksheet = workbook.Sheets[firstSheetName];

    // 解析Excel数据
    const parseResult = parseExcelData(worksheet);
    if (!parseResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'EXCEL_PARSE_FAILED',
          message: parseResult.message
        }
      }, { status: 400 });
    }

    const importData = parseResult.data!;

    // 检查数据行数限制
    if (importData.length > MAX_ROWS) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'TOO_MANY_ROWS',
          message: `导入数据超过限制，最多支持${MAX_ROWS}行数据，当前${importData.length}行`
        }
      }, { status: 400 });
    }

    if (importData.length === 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'NO_DATA_FOUND',
          message: '没有找到有效的数据行'
        }
      }, { status: 400 });
    }

    // 执行批量导入
    console.log(`📊 开始导入${importData.length}条促销活动数据`);
    const importResult = await promotionDAO.bulkImport(importData);

    console.log('✅ 批量导入完成');
    console.log(`📈 导入统计: 成功${importResult.success_count}条，失败${importResult.error_count}条`);

    return NextResponse.json({
      success: true,
      data: importResult
    });

  } catch (error) {
    console.error('❌ 批量导入促销活动失败:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'BULK_IMPORT_FAILED',
        message: '批量导入失败',
        details: error instanceof Error ? error.message : '未知错误'
      }
    }, { status: 500 });
  }
}

// ============================================================================
// 辅助函数
// ============================================================================

/**
 * 验证上传文件
 * @param file 上传的文件
 * @returns 验证结果
 */
function validateFile(file: File): { valid: boolean; message?: string } {
  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      message: `文件大小超过限制，最大支持${MAX_FILE_SIZE / 1024 / 1024}MB`
    };
  }

  // 检查文件扩展名
  const fileName = file.name.toLowerCase();
  const hasValidExtension = ALLOWED_EXTENSIONS.some(ext => fileName.endsWith(ext));
  
  if (!hasValidExtension) {
    return {
      valid: false,
      message: `不支持的文件格式，请上传${ALLOWED_EXTENSIONS.join('或')}格式的文件`
    };
  }

  return { valid: true };
}

/**
 * 解析Excel数据
 * @param worksheet Excel工作表
 * @returns 解析结果
 */
function parseExcelData(worksheet: XLSX.WorkSheet): {
  success: boolean;
  message?: string;
  data?: BulkImportPromotionInput[];
} {
  try {
    // 将工作表转换为JSON数组
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    if (jsonData.length < 2) {
      return {
        success: false,
        message: 'Excel文件至少需要包含标题行和一行数据'
      };
    }

    // 获取标题行
    const headers = jsonData[0] as string[];
    if (!headers || headers.length === 0) {
      return {
        success: false,
        message: '无法读取Excel文件的标题行'
      };
    }

    // 验证必需的列是否存在
    const requiredColumns = ['竞品名称', '票种名称', '活动名称'];
    const missingColumns = requiredColumns.filter(col => !headers.includes(col));
    
    if (missingColumns.length > 0) {
      return {
        success: false,
        message: `缺少必需的列: ${missingColumns.join(', ')}`
      };
    }

    // 创建列索引映射
    const columnIndexMap = new Map<string, number>();
    headers.forEach((header, index) => {
      if (header && COLUMN_MAPPING[header as keyof typeof COLUMN_MAPPING]) {
        columnIndexMap.set(COLUMN_MAPPING[header as keyof typeof COLUMN_MAPPING], index);
      }
    });

    // 解析数据行
    const importData: BulkImportPromotionInput[] = [];
    
    for (let i = 1; i < jsonData.length; i++) {
      const row = jsonData[i] as any[];
      
      // 跳过空行
      if (!row || row.every(cell => !cell || cell.toString().trim() === '')) {
        continue;
      }

      // 构建数据对象
      const rowData: BulkImportPromotionInput = {
        competitor_name: getCellValue(row, columnIndexMap.get('competitor_name')),
        ticket_type_name: getCellValue(row, columnIndexMap.get('ticket_type_name')),
        activity_name: getCellValue(row, columnIndexMap.get('activity_name')),
        rack_rate: getNumericValue(row, columnIndexMap.get('rack_rate')),
        promo_price: getNumericValue(row, columnIndexMap.get('promo_price')),
        sale_start_date: getDateValue(row, columnIndexMap.get('sale_start_date')),
        sale_end_date: getDateValue(row, columnIndexMap.get('sale_end_date')),
        use_start_date: getDateValue(row, columnIndexMap.get('use_start_date')),
        use_end_date: getDateValue(row, columnIndexMap.get('use_end_date')),
        sales_channel: getCellValue(row, columnIndexMap.get('sales_channel')),
        usage_rules: getCellValue(row, columnIndexMap.get('usage_rules')),
        data_source_url: getCellValue(row, columnIndexMap.get('data_source_url')),
        remarks: getCellValue(row, columnIndexMap.get('remarks'))
      };

      importData.push(rowData);
    }

    return {
      success: true,
      data: importData
    };

  } catch (error) {
    console.error('❌ 解析Excel数据失败:', error);
    return {
      success: false,
      message: `解析Excel文件失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
}

/**
 * 获取单元格字符串值
 * @param row 数据行
 * @param columnIndex 列索引
 * @returns 字符串值
 */
function getCellValue(row: any[], columnIndex?: number): string {
  if (columnIndex === undefined || columnIndex < 0 || columnIndex >= row.length) {
    return '';
  }
  
  const value = row[columnIndex];
  return value ? value.toString().trim() : '';
}

/**
 * 获取单元格数值
 * @param row 数据行
 * @param columnIndex 列索引
 * @returns 数值或undefined
 */
function getNumericValue(row: any[], columnIndex?: number): number | undefined {
  const value = getCellValue(row, columnIndex);
  if (!value) return undefined;
  
  const numValue = parseFloat(value);
  return isNaN(numValue) ? undefined : numValue;
}

/**
 * 获取单元格日期值
 * @param row 数据行
 * @param columnIndex 列索引
 * @returns 日期字符串或undefined
 */
function getDateValue(row: any[], columnIndex?: number): string | undefined {
  const value = getCellValue(row, columnIndex);
  if (!value) return undefined;
  
  // 如果是Excel日期数字，转换为日期
  if (!isNaN(Number(value))) {
    const excelDate = XLSX.SSF.parse_date_code(Number(value));
    if (excelDate) {
      const date = new Date(excelDate.y, excelDate.m - 1, excelDate.d);
      return date.toISOString().split('T')[0];
    }
  }
  
  // 如果是字符串日期，验证格式
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (dateRegex.test(value)) {
    return value;
  }
  
  return undefined;
}
