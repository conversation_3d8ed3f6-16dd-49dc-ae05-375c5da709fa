# API 接口文档

竞品分析管理系统的 RESTful API 接口文档。

## 📋 基础信息

- **Base URL**: `http://localhost:3000/api`（开发环境）
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 🔄 通用响应格式

所有 API 接口都遵循统一的响应格式：

### 成功响应

```json
{
  "success": true,
  "data": {}, // 响应数据
  "message": "操作成功", // 可选的消息
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "type": "VALIDATION_ERROR",
    "message": "验证失败",
    "details": {}, // 可选的错误详情
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 错误类型

- `NETWORK_ERROR`: 网络错误
- `VALIDATION_ERROR`: 验证错误
- `AUTHENTICATION_ERROR`: 认证错误
- `AUTHORIZATION_ERROR`: 授权错误
- `NOT_FOUND_ERROR`: 资源不存在
- `SERVER_ERROR`: 服务器错误
- `DATABASE_ERROR`: 数据库错误
- `UNKNOWN_ERROR`: 未知错误

## 📊 分页参数

支持分页的接口都接受以下查询参数：

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| page | number | 1 | 页码 |
| pageSize | number | 10 | 每页记录数 |
| sortBy | string | id | 排序字段 |
| sortOrder | string | ASC | 排序方向（ASC/DESC） |

### 分页响应格式

```json
{
  "data": [], // 当前页数据
  "total": 100, // 总记录数
  "page": 1, // 当前页码
  "pageSize": 10, // 每页记录数
  "totalPages": 10 // 总页数
}
```

## 🏢 竞品管理 API

### 获取竞品列表

```http
GET /api/competitors
```

**查询参数：**

| 参数 | 类型 | 说明 |
|------|------|------|
| competitor_name | string | 竞品名称（模糊搜索） |
| city | string | 所在城市 |
| park_type | string | 园区类型 |
| is_active | boolean | 是否活跃 |

**响应示例：**

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "competitor_id": 1,
        "competitor_name": "竞品A",
        "city": "北京",
        "park_type": "主题公园",
        "is_active": true
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 创建竞品

```http
POST /api/competitors
```

**请求体：**

```json
{
  "competitor_name": "新竞品", // 必填
  "city": "上海", // 可选
  "park_type": "水上乐园", // 可选
  "is_active": true // 可选，默认true
}
```

**响应示例：**

```json
{
  "success": true,
  "data": {
    "competitor_id": 2,
    "competitor_name": "新竞品",
    "city": "上海",
    "park_type": "水上乐园",
    "is_active": true
  },
  "message": "创建竞品成功"
}
```

### 获取单个竞品

```http
GET /api/competitors/{id}
```

**路径参数：**

| 参数 | 类型 | 说明 |
|------|------|------|
| id | number | 竞品ID |

### 更新竞品

```http
PUT /api/competitors/{id}
```

**请求体：**（所有字段都是可选的）

```json
{
  "competitor_name": "更新后的竞品名",
  "city": "深圳",
  "park_type": "游乐园",
  "is_active": false
}
```

### 部分更新竞品

```http
PATCH /api/competitors/{id}
```

**请求体：**（只更新提供的字段）

```json
{
  "is_active": false
}
```

### 删除竞品

```http
DELETE /api/competitors/{id}
```

**响应示例：**

```json
{
  "success": true,
  "data": {
    "deleted": true,
    "competitor": {
      "competitor_id": 1,
      "competitor_name": "已删除的竞品"
    }
  },
  "message": "删除竞品成功"
}
```

## 🎫 票种管理 API

### 获取票种列表

```http
GET /api/ticket-types
```

**查询参数：**

| 参数 | 类型 | 说明 |
|------|------|------|
| ticket_type_name | string | 票种名称（模糊搜索） |
| category | string | 票种分类 |

### 创建票种

```http
POST /api/ticket-types
```

**请求体：**

```json
{
  "ticket_type_name": "成人票", // 必填
  "category": "标准票" // 可选
}
```

### 其他票种操作

票种的其他 CRUD 操作与竞品 API 类似：

- `GET /api/ticket-types/{id}` - 获取单个票种
- `PUT /api/ticket-types/{id}` - 更新票种
- `PATCH /api/ticket-types/{id}` - 部分更新票种
- `DELETE /api/ticket-types/{id}` - 删除票种

## 🎯 促销活动管理 API

### 获取促销活动列表

```http
GET /api/promotions
```

**查询参数：**

| 参数 | 类型 | 说明 |
|------|------|------|
| competitor_id | number | 竞品ID |
| ticket_type_id | number | 票种ID |
| activity_name | string | 活动名称（模糊搜索） |
| sales_channel | string | 销售渠道（模糊搜索） |
| sale_date_start | string | 销售开始日期 |
| sale_date_end | string | 销售结束日期 |
| use_date_start | string | 使用开始日期 |
| use_date_end | string | 使用结束日期 |
| min_price | number | 最低价格 |
| max_price | number | 最高价格 |

**响应示例：**

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "promotion_id": 1,
        "competitor_id": 1,
        "ticket_type_id": 1,
        "activity_name": "春节特惠",
        "rack_rate": 100.00,
        "promo_price": 80.00,
        "sale_start_date": "2024-01-01",
        "sale_end_date": "2024-01-31",
        "use_start_date": "2024-01-01",
        "use_end_date": "2024-02-29",
        "sales_channel": "官网",
        "usage_rules": "限周末使用",
        "data_source_url": "https://example.com",
        "entry_date": "2024-01-01T00:00:00.000Z",
        "remarks": "备注信息",
        "competitor": {
          "competitor_id": 1,
          "competitor_name": "竞品A",
          "city": "北京",
          "park_type": "主题公园",
          "is_active": true
        },
        "ticket_type": {
          "ticket_type_id": 1,
          "ticket_type_name": "成人票",
          "category": "标准票"
        },
        "discount_rate": 0.2
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 创建促销活动

```http
POST /api/promotions
```

**请求体：**

```json
{
  "competitor_id": 1, // 必填
  "ticket_type_id": 1, // 必填
  "activity_name": "新年特惠", // 必填
  "rack_rate": 120.00, // 可选
  "promo_price": 90.00, // 可选
  "sale_start_date": "2024-01-01", // 可选，格式：YYYY-MM-DD
  "sale_end_date": "2024-01-31", // 可选
  "use_start_date": "2024-01-01", // 可选
  "use_end_date": "2024-02-29", // 可选
  "sales_channel": "APP", // 可选
  "usage_rules": "不限时间", // 可选
  "data_source_url": "https://example.com/promo", // 可选
  "remarks": "新年促销活动" // 可选
}
```

## 📈 统计分析 API

### 获取系统统计信息

```http
GET /api/statistics
```

**响应示例：**

```json
{
  "success": true,
  "data": {
    "competitors": {
      "total": 10,
      "active": 8,
      "inactive": 2,
      "cityCount": 5,
      "parkTypeCount": 3
    },
    "ticketTypes": {
      "total": 15,
      "categoryCount": 4,
      "withPromotions": 12,
      "withoutPromotions": 3
    },
    "promotions": {
      "total": 50,
      "activePromotions": 20,
      "expiredPromotions": 30,
      "avgDiscountRate": 0.25,
      "avgPrice": 85.50,
      "totalCompetitors": 8,
      "totalTicketTypes": 12
    },
    "summary": {
      "totalRecords": 75,
      "lastUpdated": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

## 🔍 错误处理示例

### 验证错误

```json
{
  "success": false,
  "error": {
    "type": "VALIDATION_ERROR",
    "message": "缺少必需字段: competitor_name",
    "details": {
      "missingFields": ["competitor_name"]
    },
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 资源不存在

```json
{
  "success": false,
  "error": {
    "type": "NOT_FOUND_ERROR",
    "message": "Competitor with ID 999 not found",
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 📝 使用示例

### JavaScript/Fetch

```javascript
// 获取竞品列表
const response = await fetch('/api/competitors?page=1&pageSize=10');
const result = await response.json();

if (result.success) {
  console.log('竞品列表:', result.data);
} else {
  console.error('错误:', result.error.message);
}

// 创建竞品
const newCompetitor = {
  competitor_name: '新竞品',
  city: '北京',
  is_active: true
};

const createResponse = await fetch('/api/competitors', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(newCompetitor)
});

const createResult = await createResponse.json();
```

### cURL

```bash
# 获取竞品列表
curl -X GET "http://localhost:3000/api/competitors?page=1&pageSize=10"

# 创建竞品
curl -X POST "http://localhost:3000/api/competitors" \
  -H "Content-Type: application/json" \
  -d '{"competitor_name":"新竞品","city":"北京","is_active":true}'

# 更新竞品
curl -X PUT "http://localhost:3000/api/competitors/1" \
  -H "Content-Type: application/json" \
  -d '{"competitor_name":"更新后的竞品名"}'

# 删除竞品
curl -X DELETE "http://localhost:3000/api/competitors/1"
```

---

更多详细信息请参考源代码中的 API 路由实现。
