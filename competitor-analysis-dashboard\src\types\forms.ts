/**
 * 表单相关的TypeScript类型定义
 * 
 * 功能说明：
 * 1. 定义表单字段的验证规则
 * 2. 提供表单状态管理类型
 * 3. 支持表单错误处理和用户反馈
 */

// ============================================================================
// 表单验证相关类型定义
// ============================================================================

/**
 * 表单字段验证错误接口
 */
export interface FieldError {
  field: string;               // 字段名称
  message: string;             // 错误消息
  code?: string;              // 错误代码
}

/**
 * 表单验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;            // 是否验证通过
  errors: FieldError[];        // 验证错误列表
}

/**
 * 表单状态枚举
 */
export enum FormStatus {
  IDLE = 'idle',              // 空闲状态
  LOADING = 'loading',        // 加载中
  SUBMITTING = 'submitting',  // 提交中
  SUCCESS = 'success',        // 成功
  ERROR = 'error'             // 错误
}

/**
 * 表单状态接口
 */
export interface FormState {
  status: FormStatus;          // 当前状态
  message?: string;           // 状态消息
  errors?: FieldError[];      // 字段错误列表
}

// ============================================================================
// 竞品表单相关类型定义
// ============================================================================

/**
 * 竞品表单数据接口
 */
export interface CompetitorFormData {
  competitor_name: string;     // 竞品名称
  city: string;               // 所在城市
  park_type: string;          // 园区类型
  is_active: boolean;         // 是否活跃
}

/**
 * 竞品表单验证规则接口
 */
export interface CompetitorFormValidation {
  competitor_name: {
    required: boolean;         // 是否必填
    minLength: number;         // 最小长度
    maxLength: number;         // 最大长度
  };
  city: {
    maxLength: number;         // 最大长度
  };
  park_type: {
    maxLength: number;         // 最大长度
  };
}

// ============================================================================
// 票种表单相关类型定义
// ============================================================================

/**
 * 票种表单数据接口
 */
export interface TicketTypeFormData {
  ticket_type_name: string;    // 票种名称
  category: string;           // 票种分类
}

/**
 * 票种表单验证规则接口
 */
export interface TicketTypeFormValidation {
  ticket_type_name: {
    required: boolean;         // 是否必填
    minLength: number;         // 最小长度
    maxLength: number;         // 最大长度
  };
  category: {
    maxLength: number;         // 最大长度
  };
}

// ============================================================================
// 促销活动表单相关类型定义
// ============================================================================

/**
 * 促销活动表单数据接口
 */
export interface PromotionFormData {
  competitor_id: number | '';  // 竞品ID
  ticket_type_id: number | ''; // 票种ID
  activity_name: string;       // 活动名称
  rack_rate: number | '';      // 门市价
  promo_price: number | '';    // 促销价
  sale_start_date: string;     // 销售开始日期
  sale_end_date: string;       // 销售结束日期
  use_start_date: string;      // 使用开始日期
  use_end_date: string;        // 使用结束日期
  sales_channel: string;       // 销售渠道
  usage_rules: string;         // 使用规则
  data_source_url: string;     // 数据来源URL
  remarks: string;             // 备注
}

/**
 * 促销活动表单验证规则接口
 */
export interface PromotionFormValidation {
  competitor_id: {
    required: boolean;         // 是否必填
  };
  ticket_type_id: {
    required: boolean;         // 是否必填
  };
  activity_name: {
    required: boolean;         // 是否必填
    minLength: number;         // 最小长度
    maxLength: number;         // 最大长度
  };
  rack_rate: {
    min: number;              // 最小值
    max: number;              // 最大值
  };
  promo_price: {
    min: number;              // 最小值
    max: number;              // 最大值
  };
  sales_channel: {
    maxLength: number;         // 最大长度
  };
  data_source_url: {
    maxLength: number;         // 最大长度
    pattern?: RegExp;         // URL格式验证
  };
}

// ============================================================================
// 日历表单相关类型定义
// ============================================================================

/**
 * 销售日历表单数据接口
 */
export interface DailySalesFormData {
  sales_date: string;          // 销售日期
  promotion_id: number | '';   // 促销活动ID
  promo_price: number | '';    // 促销价
  rack_rate: number | '';      // 门市价
  discount_rate: number | '';  // 折扣率
}

/**
 * 使用日历表单数据接口
 */
export interface DailyUsageFormData {
  usage_date: string;          // 使用日期
  promotion_id: number | '';   // 促销活动ID
  promo_price: number | '';    // 促销价
  rack_rate: number | '';      // 门市价
  discount_rate: number | '';  // 折扣率
}

// ============================================================================
// 搜索和筛选表单相关类型定义
// ============================================================================

/**
 * 搜索表单数据接口
 */
export interface SearchFormData {
  keyword: string;             // 搜索关键词
  category: string;           // 搜索分类
  dateRange: {
    start: string;            // 开始日期
    end: string;              // 结束日期
  };
  priceRange: {
    min: number | '';         // 最低价格
    max: number | '';         // 最高价格
  };
}

/**
 * 筛选表单数据接口
 */
export interface FilterFormData {
  competitor_ids: number[];    // 选中的竞品ID列表
  ticket_type_ids: number[];  // 选中的票种ID列表
  cities: string[];           // 选中的城市列表
  park_types: string[];       // 选中的园区类型列表
  sales_channels: string[];   // 选中的销售渠道列表
  is_active?: boolean;        // 是否活跃筛选
}

// ============================================================================
// 表单选项相关类型定义
// ============================================================================

/**
 * 下拉选项接口
 */
export interface SelectOption {
  value: string | number;      // 选项值
  label: string;              // 显示文本
  disabled?: boolean;         // 是否禁用
  group?: string;             // 分组名称
}

/**
 * 竞品选项接口
 */
export interface CompetitorOption extends SelectOption {
  value: number;              // 竞品ID
  city?: string;              // 城市信息
  park_type?: string;         // 园区类型
  is_active: boolean;         // 是否活跃
}

/**
 * 票种选项接口
 */
export interface TicketTypeOption extends SelectOption {
  value: number;              // 票种ID
  category?: string;          // 分类信息
}

// ============================================================================
// 表单操作相关类型定义
// ============================================================================

/**
 * 表单操作类型枚举
 */
export enum FormAction {
  CREATE = 'create',          // 创建
  UPDATE = 'update',          // 更新
  DELETE = 'delete',          // 删除
  VIEW = 'view'               // 查看
}

/**
 * 表单模式接口
 */
export interface FormMode {
  action: FormAction;         // 操作类型
  title: string;              // 表单标题
  submitText: string;         // 提交按钮文本
  readonly: boolean;          // 是否只读
}

/**
 * 表单配置接口
 */
export interface FormConfig {
  mode: FormMode;             // 表单模式
  initialData?: any;          // 初始数据
  validation?: any;           // 验证规则
  onSubmit: (data: any) => Promise<void>; // 提交处理函数
  onCancel?: () => void;      // 取消处理函数
}

// ============================================================================
// 批量操作相关类型定义
// ============================================================================

/**
 * 批量操作类型枚举
 */
export enum BatchAction {
  DELETE = 'delete',          // 批量删除
  UPDATE_STATUS = 'update_status', // 批量更新状态
  EXPORT = 'export',          // 批量导出
  IMPORT = 'import'           // 批量导入
}

/**
 * 批量操作数据接口
 */
export interface BatchOperationData {
  action: BatchAction;        // 操作类型
  selectedIds: (string | number)[]; // 选中的记录ID列表
  params?: any;              // 操作参数
}

/**
 * 批量操作结果接口
 */
export interface BatchOperationResult {
  success: boolean;           // 是否成功
  successCount: number;       // 成功数量
  failureCount: number;       // 失败数量
  errors?: string[];          // 错误信息列表
  message?: string;           // 结果消息
}
