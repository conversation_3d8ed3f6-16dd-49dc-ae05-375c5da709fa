/**
 * 单个竞品API路由
 * 
 * 功能说明：
 * 1. 处理单个竞品的查询、更新、删除操作
 * 2. 提供数据验证和错误处理
 * 3. 支持部分更新（PATCH）
 */

import { NextRequest } from 'next/server';
import { competitorDAO } from '@/lib/dao';
import {
  createSuccessResponse,
  createNotFoundResponse,
  createValidationErrorResponse,
  parseRequestBody,
  validateStringLength,
  withErrorHandling,
  createMethodNotAllowedResponse
} from '@/lib/api-utils';
import { UpdateCompetitorInput } from '@/types';

/**
 * GET /api/competitors/[id]
 * 获取单个竞品详情
 */
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const competitorId = parseInt(params.id);
  console.log(`📥 GET /api/competitors/${competitorId} - 获取竞品详情`);

  // 验证ID格式
  if (isNaN(competitorId)) {
    return createValidationErrorResponse('无效的竞品ID');
  }

  // 查询竞品
  const competitor = await competitorDAO.findById(competitorId);
  if (!competitor) {
    return createNotFoundResponse('竞品', competitorId);
  }

  console.log(`✅ 成功获取竞品详情: ${competitor.competitor_name}`);
  return createSuccessResponse(competitor, '获取竞品详情成功');
});

/**
 * PUT /api/competitors/[id]
 * 完整更新竞品信息
 */
export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const competitorId = parseInt(params.id);
  console.log(`📥 PUT /api/competitors/${competitorId} - 完整更新竞品`);

  // 验证ID格式
  if (isNaN(competitorId)) {
    return createValidationErrorResponse('无效的竞品ID');
  }

  // 检查竞品是否存在
  const existingCompetitor = await competitorDAO.findById(competitorId);
  if (!existingCompetitor) {
    return createNotFoundResponse('竞品', competitorId);
  }

  // 解析请求体
  const body = await parseRequestBody<UpdateCompetitorInput>(request);
  console.log('📝 更新数据:', body);

  // 验证字段（PUT要求提供完整数据）
  if (body.competitor_name !== undefined) {
    if (!validateStringLength(body.competitor_name, 1, 100)) {
      return createValidationErrorResponse('竞品名称长度必须在1-100个字符之间');
    }

    // 检查名称是否与其他竞品冲突
    const nameExists = await competitorDAO.isNameExists(body.competitor_name, competitorId);
    if (nameExists) {
      return createValidationErrorResponse('竞品名称已存在');
    }
  }

  if (body.city !== undefined && !validateStringLength(body.city, 0, 50)) {
    return createValidationErrorResponse('城市名称长度不能超过50个字符');
  }

  if (body.park_type !== undefined && !validateStringLength(body.park_type, 0, 50)) {
    return createValidationErrorResponse('园区类型长度不能超过50个字符');
  }

  // 更新竞品
  const updateSuccess = await competitorDAO.update(competitorId, body);
  if (!updateSuccess) {
    return createValidationErrorResponse('更新失败，可能是数据未发生变化');
  }

  // 获取更新后的竞品详情
  const updatedCompetitor = await competitorDAO.findById(competitorId);

  console.log(`✅ 成功更新竞品: ${updatedCompetitor?.competitor_name}`);
  return createSuccessResponse(updatedCompetitor, '更新竞品成功');
});

/**
 * PATCH /api/competitors/[id]
 * 部分更新竞品信息
 */
export const PATCH = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const competitorId = parseInt(params.id);
  console.log(`📥 PATCH /api/competitors/${competitorId} - 部分更新竞品`);

  // 验证ID格式
  if (isNaN(competitorId)) {
    return createValidationErrorResponse('无效的竞品ID');
  }

  // 检查竞品是否存在
  const existingCompetitor = await competitorDAO.findById(competitorId);
  if (!existingCompetitor) {
    return createNotFoundResponse('竞品', competitorId);
  }

  // 解析请求体
  const body = await parseRequestBody<UpdateCompetitorInput>(request);
  console.log('📝 部分更新数据:', body);

  // 验证提供的字段
  if (body.competitor_name !== undefined) {
    if (!validateStringLength(body.competitor_name, 1, 100)) {
      return createValidationErrorResponse('竞品名称长度必须在1-100个字符之间');
    }

    // 检查名称是否与其他竞品冲突
    const nameExists = await competitorDAO.isNameExists(body.competitor_name, competitorId);
    if (nameExists) {
      return createValidationErrorResponse('竞品名称已存在');
    }
  }

  if (body.city !== undefined && !validateStringLength(body.city, 0, 50)) {
    return createValidationErrorResponse('城市名称长度不能超过50个字符');
  }

  if (body.park_type !== undefined && !validateStringLength(body.park_type, 0, 50)) {
    return createValidationErrorResponse('园区类型长度不能超过50个字符');
  }

  // 更新竞品
  const updateSuccess = await competitorDAO.update(competitorId, body);
  if (!updateSuccess) {
    return createValidationErrorResponse('更新失败，可能是数据未发生变化');
  }

  // 获取更新后的竞品详情
  const updatedCompetitor = await competitorDAO.findById(competitorId);

  console.log(`✅ 成功部分更新竞品: ${updatedCompetitor?.competitor_name}`);
  return createSuccessResponse(updatedCompetitor, '部分更新竞品成功');
});

/**
 * DELETE /api/competitors/[id]
 * 删除竞品
 */
export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const competitorId = parseInt(params.id);
  console.log(`📥 DELETE /api/competitors/${competitorId} - 删除竞品`);

  // 验证ID格式
  if (isNaN(competitorId)) {
    return createValidationErrorResponse('无效的竞品ID');
  }

  // 检查竞品是否存在
  const existingCompetitor = await competitorDAO.findById(competitorId);
  if (!existingCompetitor) {
    return createNotFoundResponse('竞品', competitorId);
  }

  // 检查是否有关联的促销活动
  // 注意：这里需要先导入promotionDAO，但为了避免循环依赖，我们先简化处理
  // 在实际应用中，可以通过外键约束来处理级联删除

  // 删除竞品
  const deleteSuccess = await competitorDAO.delete(competitorId);
  if (!deleteSuccess) {
    return createValidationErrorResponse('删除失败');
  }

  console.log(`✅ 成功删除竞品: ${existingCompetitor.competitor_name}`);
  return createSuccessResponse(
    { deleted: true, competitor: existingCompetitor },
    '删除竞品成功'
  );
});

/**
 * 处理不支持的HTTP方法
 */
export async function POST(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'PUT', 'PATCH', 'DELETE']);
}
