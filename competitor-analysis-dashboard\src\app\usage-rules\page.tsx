/**
 * 使用规则管理页面
 * 
 * 功能说明：
 * 1. 显示所有使用规则列表
 * 2. 支持添加、编辑、删除使用规则
 * 3. 支持启用/禁用规则状态管理
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  ScrollableDialogHeader,
  ScrollableDialogBody,
  ScrollableDialogFooter
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Power,
  PowerOff,
  AlertCircle,
  CheckCircle2,
  Loader2
} from 'lucide-react';
import { UsageRule } from '@/types';
import { MainLayout } from '@/components/layout/main-layout';

/**
 * 使用规则管理页面组件
 */
export default function UsageRulesPage() {
  // 状态管理
  const [usageRules, setUsageRules] = useState<UsageRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  // 对话框状态
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedRule, setSelectedRule] = useState<UsageRule | null>(null);

  // 表单状态
  const [formData, setFormData] = useState({
    rule_description: '',
    is_active: true
  });
  const [submitting, setSubmitting] = useState(false);

  /**
   * 加载使用规则列表
   */
  const loadUsageRules = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await fetch('/api/usage-rules');
      const result = await response.json();
      
      if (result.success) {
        setUsageRules(result.data || []);
      } else {
        setError(result.message || '加载使用规则失败');
      }
    } catch (error) {
      console.error('加载使用规则失败:', error);
      setError('加载使用规则失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 创建使用规则
   */
  const handleCreate = async () => {
    if (!formData.rule_description.trim()) {
      setError('规则描述不能为空');
      return;
    }

    try {
      setSubmitting(true);
      setError('');

      const response = await fetch('/api/usage-rules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rule_description: formData.rule_description.trim(),
          is_active: formData.is_active
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('使用规则创建成功');
        setShowCreateDialog(false);
        setFormData({ rule_description: '', is_active: true });
        await loadUsageRules();
      } else {
        setError(result.message || '创建使用规则失败');
      }
    } catch (error) {
      console.error('创建使用规则失败:', error);
      setError('创建使用规则失败，请检查网络连接');
    } finally {
      setSubmitting(false);
    }
  };

  /**
   * 更新使用规则
   */
  const handleUpdate = async () => {
    if (!selectedRule || !formData.rule_description.trim()) {
      setError('规则描述不能为空');
      return;
    }

    try {
      setSubmitting(true);
      setError('');

      const response = await fetch(`/api/usage-rules/${selectedRule.rule_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rule_description: formData.rule_description.trim(),
          is_active: formData.is_active
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('使用规则更新成功');
        setShowEditDialog(false);
        setSelectedRule(null);
        setFormData({ rule_description: '', is_active: true });
        await loadUsageRules();
      } else {
        setError(result.message || '更新使用规则失败');
      }
    } catch (error) {
      console.error('更新使用规则失败:', error);
      setError('更新使用规则失败，请检查网络连接');
    } finally {
      setSubmitting(false);
    }
  };

  /**
   * 删除使用规则
   */
  const handleDelete = async () => {
    if (!selectedRule) return;

    try {
      setSubmitting(true);
      setError('');

      const response = await fetch(`/api/usage-rules/${selectedRule.rule_id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('使用规则删除成功');
        setShowDeleteDialog(false);
        setSelectedRule(null);
        await loadUsageRules();
      } else {
        setError(result.message || '删除使用规则失败');
      }
    } catch (error) {
      console.error('删除使用规则失败:', error);
      setError('删除使用规则失败，请检查网络连接');
    } finally {
      setSubmitting(false);
    }
  };

  /**
   * 切换规则状态
   */
  const handleToggleStatus = async (rule: UsageRule) => {
    try {
      setError('');

      const response = await fetch(`/api/usage-rules/${rule.rule_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_active: !rule.is_active
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess(`规则已${rule.is_active ? '禁用' : '启用'}`);
        await loadUsageRules();
      } else {
        setError(result.message || '状态切换失败');
      }
    } catch (error) {
      console.error('状态切换失败:', error);
      setError('状态切换失败，请检查网络连接');
    }
  };

  /**
   * 打开编辑对话框
   */
  const openEditDialog = (rule: UsageRule) => {
    setSelectedRule(rule);
    setFormData({
      rule_description: rule.rule_description,
      is_active: rule.is_active
    });
    setShowEditDialog(true);
  };

  /**
   * 打开删除对话框
   */
  const openDeleteDialog = (rule: UsageRule) => {
    setSelectedRule(rule);
    setShowDeleteDialog(true);
  };

  /**
   * 过滤规则列表
   */
  const filteredRules = usageRules.filter(rule =>
    rule.rule_description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  /**
   * 清除消息
   */
  const clearMessages = () => {
    setError('');
    setSuccess('');
  };

  // 页面加载时获取数据
  useEffect(() => {
    loadUsageRules();
  }, []);

  // 自动清除消息
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(clearMessages, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success]);

  return (
    <MainLayout currentPath="/usage-rules">
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">使用规则管理</h1>
          <p className="text-muted-foreground">
            管理促销活动的使用规则，包括规则的创建、编辑和状态管理
          </p>
        </div>

      {/* 消息提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <CheckCircle2 className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* 操作栏 */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div>
              <CardTitle>规则列表</CardTitle>
              <CardDescription>管理所有使用规则</CardDescription>
            </div>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              添加规则
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* 搜索栏 */}
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索规则描述..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* 规则表格 */}
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">加载中...</span>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>规则描述</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRules.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                      {searchTerm ? '没有找到匹配的规则' : '暂无使用规则'}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRules.map((rule) => (
                    <TableRow key={rule.rule_id}>
                      <TableCell className="font-medium">{rule.rule_id}</TableCell>
                      <TableCell>{rule.rule_description}</TableCell>
                      <TableCell>
                        <Badge variant={rule.is_active ? 'default' : 'secondary'}>
                          {rule.is_active ? '启用' : '禁用'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleToggleStatus(rule)}
                            title={rule.is_active ? '禁用规则' : '启用规则'}
                          >
                            {rule.is_active ? (
                              <PowerOff className="h-4 w-4" />
                            ) : (
                              <Power className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(rule)}
                            title="编辑规则"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDeleteDialog(rule)}
                            title="删除规则"
                            disabled={rule.rule_id === 1} // 禁止删除"无限制"规则
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* 创建规则对话框 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogTitle className="sr-only">添加使用规则</DialogTitle>
          <DialogDescription className="sr-only">
            创建新的使用规则，用于促销活动的限制条件
          </DialogDescription>

          <ScrollableDialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              添加使用规则
            </DialogTitle>
            <DialogDescription>
              创建新的使用规则，用于促销活动的限制条件
            </DialogDescription>
          </ScrollableDialogHeader>

          <ScrollableDialogBody>
            <form className="space-y-6" id="create-rule-form">
              <div className="space-y-2">
                <Label htmlFor="rule_description" className="text-sm font-medium">
                  规则描述 *
                </Label>
                <Textarea
                  id="rule_description"
                  placeholder="请输入规则描述，如：仅限平日使用、需至少提前1天预约等"
                  value={formData.rule_description}
                  onChange={(e) => setFormData({ ...formData, rule_description: e.target.value })}
                  rows={4}
                  className="resize-none"
                />
                <p className="text-xs text-muted-foreground">
                  请详细描述使用规则的具体限制条件，便于用户理解
                </p>
              </div>
            </form>
          </ScrollableDialogBody>

          <ScrollableDialogFooter>
            <Button
              type="submit"
              form="create-rule-form"
              onClick={handleCreate}
              disabled={submitting}
              className="min-w-[100px]"
            >
              {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              创建规则
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              disabled={submitting}
            >
              取消
            </Button>
          </ScrollableDialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑规则对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogTitle className="sr-only">编辑使用规则</DialogTitle>
          <DialogDescription className="sr-only">
            修改使用规则的描述和状态
          </DialogDescription>

          <ScrollableDialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              编辑使用规则
            </DialogTitle>
            <DialogDescription>
              修改使用规则的描述和状态
            </DialogDescription>
          </ScrollableDialogHeader>

          <ScrollableDialogBody>
            <form className="space-y-6" id="edit-rule-form">
              <div className="space-y-2">
                <Label htmlFor="edit_rule_description" className="text-sm font-medium">
                  规则描述 *
                </Label>
                <Textarea
                  id="edit_rule_description"
                  placeholder="请输入规则描述，如：仅限平日使用、需至少提前1天预约等"
                  value={formData.rule_description}
                  onChange={(e) => setFormData({ ...formData, rule_description: e.target.value })}
                  rows={4}
                  className="resize-none"
                />
                <p className="text-xs text-muted-foreground">
                  请详细描述使用规则的具体限制条件，便于用户理解
                </p>
              </div>
            </form>
          </ScrollableDialogBody>

          <ScrollableDialogFooter>
            <Button
              type="submit"
              form="edit-rule-form"
              onClick={handleUpdate}
              disabled={submitting}
              className="min-w-[100px]"
            >
              {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              更新规则
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowEditDialog(false)}
              disabled={submitting}
            >
              取消
            </Button>
          </ScrollableDialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              确定要删除使用规则 &quot;{selectedRule?.rule_description}&quot; 吗？
              <br />
              <span className="text-red-600 font-medium">
                注意：删除后使用此规则的促销活动可能受到影响。
              </span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={submitting}>
              {submitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </MainLayout>
  );
}
