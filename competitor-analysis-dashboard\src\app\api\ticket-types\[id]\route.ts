/**
 * 单个票种API路由
 * 
 * 功能说明：
 * 1. 处理单个票种的查询、更新、删除操作
 * 2. 提供数据验证和错误处理
 * 3. 支持部分更新（PATCH）
 */

import { NextRequest } from 'next/server';
import { ticketTypeDAO } from '@/lib/dao';
import {
  createSuccessResponse,
  createNotFoundResponse,
  createValidationErrorResponse,
  parseRequestBody,
  validateStringLength,
  withErrorHandling,
  createMethodNotAllowedResponse
} from '@/lib/api-utils';
import { UpdateTicketTypeInput } from '@/types';

/**
 * GET /api/ticket-types/[id]
 * 获取单个票种详情
 */
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const ticketTypeId = parseInt(params.id);
  console.log(`📥 GET /api/ticket-types/${ticketTypeId} - 获取票种详情`);

  // 验证ID格式
  if (isNaN(ticketTypeId)) {
    return createValidationErrorResponse('无效的票种ID');
  }

  // 查询票种
  const ticketType = await ticketTypeDAO.findById(ticketTypeId);
  if (!ticketType) {
    return createNotFoundResponse('票种', ticketTypeId);
  }

  console.log(`✅ 成功获取票种详情: ${ticketType.ticket_type_name}`);
  return createSuccessResponse(ticketType, '获取票种详情成功');
});

/**
 * PUT /api/ticket-types/[id]
 * 完整更新票种信息
 */
export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const ticketTypeId = parseInt(params.id);
  console.log(`📥 PUT /api/ticket-types/${ticketTypeId} - 完整更新票种`);

  // 验证ID格式
  if (isNaN(ticketTypeId)) {
    return createValidationErrorResponse('无效的票种ID');
  }

  // 检查票种是否存在
  const existingTicketType = await ticketTypeDAO.findById(ticketTypeId);
  if (!existingTicketType) {
    return createNotFoundResponse('票种', ticketTypeId);
  }

  // 解析请求体
  const body = await parseRequestBody<UpdateTicketTypeInput>(request);
  console.log('📝 更新数据:', body);

  // 验证字段
  if (body.ticket_type_name !== undefined) {
    if (!validateStringLength(body.ticket_type_name, 1, 100)) {
      return createValidationErrorResponse('票种名称长度必须在1-100个字符之间');
    }

    // 检查名称是否与其他票种冲突
    const nameExists = await ticketTypeDAO.isNameExists(body.ticket_type_name, ticketTypeId);
    if (nameExists) {
      return createValidationErrorResponse('票种名称已存在');
    }
  }

  if (body.category !== undefined && !validateStringLength(body.category, 0, 50)) {
    return createValidationErrorResponse('票种分类长度不能超过50个字符');
  }

  // 更新票种
  const updateSuccess = await ticketTypeDAO.update(ticketTypeId, body);
  if (!updateSuccess) {
    return createValidationErrorResponse('更新失败，可能是数据未发生变化');
  }

  // 获取更新后的票种详情
  const updatedTicketType = await ticketTypeDAO.findById(ticketTypeId);

  console.log(`✅ 成功更新票种: ${updatedTicketType?.ticket_type_name}`);
  return createSuccessResponse(updatedTicketType, '更新票种成功');
});

/**
 * PATCH /api/ticket-types/[id]
 * 部分更新票种信息
 */
export const PATCH = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const ticketTypeId = parseInt(params.id);
  console.log(`📥 PATCH /api/ticket-types/${ticketTypeId} - 部分更新票种`);

  // 验证ID格式
  if (isNaN(ticketTypeId)) {
    return createValidationErrorResponse('无效的票种ID');
  }

  // 检查票种是否存在
  const existingTicketType = await ticketTypeDAO.findById(ticketTypeId);
  if (!existingTicketType) {
    return createNotFoundResponse('票种', ticketTypeId);
  }

  // 解析请求体
  const body = await parseRequestBody<UpdateTicketTypeInput>(request);
  console.log('📝 部分更新数据:', body);

  // 验证提供的字段
  if (body.ticket_type_name !== undefined) {
    if (!validateStringLength(body.ticket_type_name, 1, 100)) {
      return createValidationErrorResponse('票种名称长度必须在1-100个字符之间');
    }

    // 检查名称是否与其他票种冲突
    const nameExists = await ticketTypeDAO.isNameExists(body.ticket_type_name, ticketTypeId);
    if (nameExists) {
      return createValidationErrorResponse('票种名称已存在');
    }
  }

  if (body.category !== undefined && !validateStringLength(body.category, 0, 50)) {
    return createValidationErrorResponse('票种分类长度不能超过50个字符');
  }

  // 更新票种
  const updateSuccess = await ticketTypeDAO.update(ticketTypeId, body);
  if (!updateSuccess) {
    return createValidationErrorResponse('更新失败，可能是数据未发生变化');
  }

  // 获取更新后的票种详情
  const updatedTicketType = await ticketTypeDAO.findById(ticketTypeId);

  console.log(`✅ 成功部分更新票种: ${updatedTicketType?.ticket_type_name}`);
  return createSuccessResponse(updatedTicketType, '部分更新票种成功');
});

/**
 * DELETE /api/ticket-types/[id]
 * 删除票种
 */
export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const ticketTypeId = parseInt(params.id);
  console.log(`📥 DELETE /api/ticket-types/${ticketTypeId} - 删除票种`);

  // 验证ID格式
  if (isNaN(ticketTypeId)) {
    return createValidationErrorResponse('无效的票种ID');
  }

  // 检查票种是否存在
  const existingTicketType = await ticketTypeDAO.findById(ticketTypeId);
  if (!existingTicketType) {
    return createNotFoundResponse('票种', ticketTypeId);
  }

  // 删除票种
  const deleteSuccess = await ticketTypeDAO.delete(ticketTypeId);
  if (!deleteSuccess) {
    return createValidationErrorResponse('删除失败');
  }

  console.log(`✅ 成功删除票种: ${existingTicketType.ticket_type_name}`);
  return createSuccessResponse(
    { deleted: true, ticketType: existingTicketType },
    '删除票种成功'
  );
});

/**
 * 处理不支持的HTTP方法
 */
export async function POST(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'PUT', 'PATCH', 'DELETE']);
}
