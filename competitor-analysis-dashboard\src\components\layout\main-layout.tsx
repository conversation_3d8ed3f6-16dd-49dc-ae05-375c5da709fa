/**
 * 主布局组件
 * 
 * 功能说明：
 * 1. 提供应用程序的主要布局结构
 * 2. 包含导航栏、侧边栏、主内容区域
 * 3. 响应式设计，支持移动端
 * 4. 可配置的布局选项
 */

import React, { useState } from 'react';
import { Menu, X, Home, Database, BarChart3, Settings, Users, Ticket, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { MobileBottomNav, MobileDrawerNav, MobileTopNav } from './mobile-nav';

// ============================================================================
// 导航菜单配置
// ============================================================================

export interface MenuItem {
  key: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  children?: MenuItem[];
}

const defaultMenuItems: MenuItem[] = [
  {
    key: 'dashboard',
    label: '仪表板',
    icon: Home,
    href: '/'
  },
  {
    key: 'competitors',
    label: '竞品管理',
    icon: Users,
    href: '/competitors'
  },
  {
    key: 'ticket-types',
    label: '票种管理',
    icon: Ticket,
    href: '/ticket-types'
  },
  {
    key: 'promotions',
    label: '促销活动',
    icon: Database,
    href: '/promotions'
  },
  {
    key: 'usage-rules',
    label: '使用规则',
    icon: FileText,
    href: '/usage-rules'
  },
  {
    key: 'analytics',
    label: '数据分析',
    icon: BarChart3,
    href: '/analytics'
  },
  {
    key: 'settings',
    label: '系统设置',
    icon: Settings,
    href: '/settings'
  }
];

// ============================================================================
// 布局组件接口
// ============================================================================

export interface MainLayoutProps {
  children: React.ReactNode;
  menuItems?: MenuItem[];
  currentPath?: string;
  title?: string;
  className?: string;
}

// ============================================================================
// 主布局组件实现
// ============================================================================

export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  menuItems = defaultMenuItems,
  currentPath = '/',
  title = '竞品分析管理系统',
  className
}) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // 渲染菜单项
  const renderMenuItem = (item: MenuItem) => {
    const isActive = currentPath === item.href || currentPath.startsWith(item.href + '/');
    const Icon = item.icon;

    return (
      <a
        key={item.key}
        href={item.href}
        className={cn(
          'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',
          isActive
            ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'
            : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
        )}
        onClick={() => setSidebarOpen(false)}
      >
        <Icon className="mr-3 h-5 w-5" />
        {item.label}
      </a>
    );
  };

  return (
    <div className={cn('min-h-screen bg-gray-50', className)}>
      {/* 移动端导航组件 */}
      <MobileDrawerNav
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        currentPath={currentPath}
      />

      {/* 桌面端侧边栏 */}
      <div
        className={cn(
          'hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-50 lg:w-64 lg:bg-white lg:shadow-lg lg:block'
        )}
      >
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <h1 className="text-lg font-semibold text-gray-900 truncate">
            {title}
          </h1>
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {menuItems.map(renderMenuItem)}
        </nav>

        {/* 侧边栏底部 */}
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            版本 1.0.0
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="lg:pl-64">
        {/* 移动端顶部导航 */}
        <MobileTopNav
          title={menuItems.find(item => item.href === currentPath)?.label || '首页'}
          onMenuClick={() => setSidebarOpen(true)}
        />

        {/* 桌面端顶部导航栏 */}
        <header className="hidden lg:block bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6">

            {/* 页面标题 */}
            <div className="flex-1 lg:flex-none">
              <h2 className="text-xl font-semibold text-gray-900">
                {/* 这里可以根据当前路径动态显示页面标题 */}
                {menuItems.find(item => item.href === currentPath)?.label || '首页'}
              </h2>
            </div>

            {/* 用户操作区域 */}
            <div className="flex items-center space-x-4">
              {/* 通知按钮 */}
              <Button variant="ghost" size="sm">
                <span className="sr-only">通知</span>
                <div className="h-5 w-5 rounded-full bg-gray-300" />
              </Button>

              {/* 用户头像 */}
              <Button variant="ghost" size="sm">
                <span className="sr-only">用户菜单</span>
                <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                  U
                </div>
              </Button>
            </div>
          </div>
        </header>

        {/* 主内容 */}
        <main className="flex-1 pb-16 lg:pb-0">
          <div className="p-4 sm:p-6 lg:p-8">
            {children}
          </div>
        </main>
      </div>

      {/* 移动端底部导航 */}
      <MobileBottomNav currentPath={currentPath} />
    </div>
  );
};

// ============================================================================
// 页面容器组件
// ============================================================================

export interface PageContainerProps {
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export const PageContainer: React.FC<PageContainerProps> = ({
  title,
  description,
  actions,
  children,
  className
}) => {
  return (
    <div className={cn('space-y-6', className)}>
      {/* 页面头部 */}
      {(title || description || actions) && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            {title && (
              <h1 className="text-2xl font-bold text-gray-900">
                {title}
              </h1>
            )}
            {description && (
              <p className="mt-1 text-sm text-gray-600">
                {description}
              </p>
            )}
          </div>
          {actions && (
            <div className="flex gap-2">
              {actions}
            </div>
          )}
        </div>
      )}

      {/* 页面内容 */}
      <div>
        {children}
      </div>
    </div>
  );
};

// ============================================================================
// 卡片容器组件
// ============================================================================

export interface CardContainerProps {
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export const CardContainer: React.FC<CardContainerProps> = ({
  title,
  description,
  actions,
  children,
  className
}) => {
  return (
    <div className={cn('bg-white rounded-lg shadow border border-gray-200', className)}>
      {/* 卡片头部 */}
      {(title || description || actions) && (
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <h3 className="text-lg font-medium text-gray-900">
                  {title}
                </h3>
              )}
              {description && (
                <p className="mt-1 text-sm text-gray-600">
                  {description}
                </p>
              )}
            </div>
            {actions && (
              <div className="flex gap-2">
                {actions}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 卡片内容 */}
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};

// ============================================================================
// 导出所有组件
// ============================================================================

export {
  MainLayout as default,
  PageContainer,
  CardContainer
};
