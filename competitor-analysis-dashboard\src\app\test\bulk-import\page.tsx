/**
 * 批量导入功能测试页面
 * 
 * 功能说明：
 * 1. 测试Excel模板下载功能
 * 2. 测试批量导入API
 * 3. 验证数据验证逻辑
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { MainLayout, PageContainer } from '@/components/layout/main-layout';
import { BulkImportDialog } from '@/components/promotions/bulk-import-dialog';
import { BulkImportResult } from '@/types';

export default function BulkImportTestPage() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [lastResult, setLastResult] = useState<BulkImportResult | null>(null);

  // 测试模板下载
  const testTemplateDownload = async () => {
    try {
      console.log('🧪 测试模板下载功能');
      
      const response = await fetch('/api/promotions/template');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'test-template.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        console.log('✅ 模板下载测试成功');
        alert('模板下载测试成功！');
      } else {
        throw new Error('模板下载失败');
      }
    } catch (error) {
      console.error('❌ 模板下载测试失败:', error);
      alert('模板下载测试失败：' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  // 测试API连接
  const testApiConnection = async () => {
    try {
      console.log('🧪 测试API连接');
      
      // 测试模板API
      const templateResponse = await fetch('/api/promotions/template', { method: 'HEAD' });
      console.log('模板API状态:', templateResponse.status);
      
      // 测试促销活动列表API
      const promotionsResponse = await fetch('/api/promotions?page=1&pageSize=1');
      const promotionsResult = await promotionsResponse.json();
      console.log('促销活动API响应:', promotionsResult);
      
      alert('API连接测试完成，请查看控制台输出');
    } catch (error) {
      console.error('❌ API连接测试失败:', error);
      alert('API连接测试失败：' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  // 处理导入成功
  const handleImportSuccess = (result: BulkImportResult) => {
    console.log('✅ 导入测试完成:', result);
    setLastResult(result);
    setDialogOpen(false);
  };

  return (
    <MainLayout currentPath="/test/bulk-import">
      <PageContainer
        title="批量导入功能测试"
        description="测试促销活动批量导入功能的各个组件"
      >
        <div className="space-y-6">
          {/* 测试按钮区域 */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">功能测试</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button onClick={testTemplateDownload} variant="outline">
                测试模板下载
              </Button>
              <Button onClick={testApiConnection} variant="outline">
                测试API连接
              </Button>
              <Button onClick={() => setDialogOpen(true)}>
                测试批量导入对话框
              </Button>
            </div>
          </Card>

          {/* 测试说明 */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">测试说明</h3>
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-medium">1. 模板下载测试</h4>
                <p className="text-gray-600">
                  点击"测试模板下载"按钮，验证Excel模板是否能正常生成和下载。
                  成功后会自动下载一个名为"test-template.xlsx"的文件。
                </p>
              </div>
              
              <div>
                <h4 className="font-medium">2. API连接测试</h4>
                <p className="text-gray-600">
                  点击"测试API连接"按钮，验证相关API端点是否正常响应。
                  测试结果会在浏览器控制台中显示。
                </p>
              </div>
              
              <div>
                <h4 className="font-medium">3. 批量导入对话框测试</h4>
                <p className="text-gray-600">
                  点击"测试批量导入对话框"按钮，打开批量导入对话框进行完整的导入流程测试。
                  可以下载模板、填写测试数据并上传验证整个流程。
                </p>
              </div>
            </div>
          </Card>

          {/* 最后导入结果 */}
          {lastResult && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">最后导入结果</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium">总计：</span>
                    <span>{lastResult.total_rows} 条</span>
                  </div>
                  <div>
                    <span className="font-medium text-green-600">成功：</span>
                    <span>{lastResult.success_count} 条</span>
                  </div>
                  <div>
                    <span className="font-medium text-red-600">失败：</span>
                    <span>{lastResult.error_count} 条</span>
                  </div>
                  <div>
                    <span className="font-medium">导入ID：</span>
                    <span>{lastResult.imported_ids.length} 个</span>
                  </div>
                </div>
                
                {lastResult.errors.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-medium text-red-600 mb-2">错误详情：</h4>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {lastResult.errors.map((error, index) => (
                        <div key={index} className="text-xs bg-red-50 p-2 rounded">
                          第 {error.row_number} 行: {error.error_message}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          )}

          {/* 开发说明 */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">开发说明</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <p>• 批量导入功能已完成开发，包含Excel模板生成、文件上传、数据验证和批量创建等功能</p>
              <p>• 支持的文件格式：.xlsx 和 .xls，最大文件大小10MB，最大导入行数1000条</p>
              <p>• 数据验证包括：必填字段验证、数据类型验证、业务规则验证</p>
              <p>• 错误处理：逐行验证，提供详细错误信息，支持部分成功导入</p>
              <p>• 相关文件：</p>
              <ul className="ml-4 space-y-1">
                <li>- API: /api/promotions/template (模板下载)</li>
                <li>- API: /api/promotions/bulk-import (批量导入)</li>
                <li>- 组件: /components/promotions/bulk-import-dialog.tsx</li>
                <li>- DAO: /lib/dao/promotion-dao.ts (扩展了批量导入方法)</li>
                <li>- 类型: /types/database.ts (新增批量导入相关类型)</li>
              </ul>
            </div>
          </Card>
        </div>

        {/* 批量导入对话框 */}
        <BulkImportDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          onImportSuccess={handleImportSuccess}
        />
      </PageContainer>
    </MainLayout>
  );
}
