/**
 * 票种数据访问对象(DAO)类
 * 
 * 功能说明：
 * 1. 实现票种表的CRUD操作
 * 2. 提供票种特有的查询方法
 * 3. 支持按分类筛选票种
 * 4. 提供票种统计和分析功能
 */

import { BaseDAO } from './base-dao';
import { executeQuery } from '../database';
import { 
  TicketType, 
  CreateTicketTypeInput, 
  UpdateTicketTypeInput,
  TicketTypeFilters,
  PaginationParams,
  PaginatedResult
} from '@/types';

/**
 * 票种DAO类
 * 继承基础DAO类，实现票种特有的数据操作
 */
export class TicketTypeDAO extends BaseDAO<TicketType, CreateTicketTypeInput, UpdateTicketTypeInput> {
  constructor() {
    super('TicketTypes', 'ticket_type_id');
  }

  /**
   * 根据筛选条件查询票种列表（分页）
   * @param params 分页参数
   * @param filters 筛选条件
   * @returns 分页查询结果
   */
  async findWithFilters(
    params: PaginationParams,
    filters: TicketTypeFilters = {}
  ): Promise<PaginatedResult<TicketType>> {
    try {
      console.log('🔍 根据筛选条件查询票种列表');
      console.log('📝 筛选条件:', filters);

      const whereConditions: string[] = [];
      const whereParams: any[] = [];

      // 按票种名称搜索
      if (filters.ticket_type_name) {
        whereConditions.push('ticket_type_name LIKE ?');
        whereParams.push(`%${filters.ticket_type_name}%`);
      }

      // 按分类筛选
      if (filters.category) {
        whereConditions.push('category = ?');
        whereParams.push(filters.category);
      }

      const whereClause = whereConditions.length > 0 ? whereConditions.join(' AND ') : '';

      return await this.findWithPagination(params, whereClause, whereParams);
    } catch (error) {
      console.error('❌ 根据筛选条件查询票种失败:', error);
      throw new Error(`查询票种失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据票种名称查询（精确匹配）
   * @param ticketTypeName 票种名称
   * @returns 票种信息
   */
  async findByName(ticketTypeName: string): Promise<TicketType | null> {
    try {
      console.log(`🔍 查询票种名称为"${ticketTypeName}"的记录`);

      const query = 'SELECT * FROM TicketTypes WHERE ticket_type_name = ?';
      const results = await executeQuery<TicketType>(query, [ticketTypeName]);

      if (results.length === 0) {
        console.log(`⚠️ 未找到名称为"${ticketTypeName}"的票种`);
        return null;
      }

      console.log('✅ 成功查询到票种信息');
      return results[0];
    } catch (error) {
      console.error('❌ 根据名称查询票种失败:', error);
      throw new Error(`查询票种失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据分类获取票种列表
   * @param category 票种分类
   * @returns 票种列表
   */
  async findByCategory(category: string): Promise<TicketType[]> {
    try {
      console.log(`🔍 查询分类"${category}"的票种列表`);

      const query = `
        SELECT * FROM TicketTypes 
        WHERE category = ? 
        ORDER BY ticket_type_name ASC
      `;
      const results = await executeQuery<TicketType>(query, [category]);

      console.log(`✅ 成功查询到${results.length}个票种`);
      return results;
    } catch (error) {
      console.error('❌ 根据分类查询票种失败:', error);
      throw new Error(`查询票种失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取所有票种分类列表
   * @returns 分类列表
   */
  async getAllCategories(): Promise<string[]> {
    try {
      console.log('🔍 获取所有票种分类列表');

      const query = `
        SELECT DISTINCT category 
        FROM TicketTypes 
        WHERE category IS NOT NULL AND category != '' 
        ORDER BY category ASC
      `;
      const results = await executeQuery<{ category: string }>(query);

      const categories = results.map(row => row.category);
      console.log(`✅ 成功获取${categories.length}个分类`);
      return categories;
    } catch (error) {
      console.error('❌ 获取分类列表失败:', error);
      throw new Error(`获取分类列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 搜索票种（模糊匹配票种名称）
   * @param keyword 搜索关键词
   * @param limit 返回结果数量限制
   * @returns 票种列表
   */
  async searchByKeyword(keyword: string, limit: number = 10): Promise<TicketType[]> {
    try {
      console.log(`🔍 搜索票种，关键词: "${keyword}"`);

      const query = `
        SELECT * FROM TicketTypes 
        WHERE ticket_type_name LIKE ? OR category LIKE ?
        ORDER BY 
          CASE 
            WHEN ticket_type_name = ? THEN 1
            WHEN ticket_type_name LIKE ? THEN 2
            ELSE 3
          END,
          ticket_type_name ASC
        LIMIT ?
      `;

      const searchPattern = `%${keyword}%`;
      const results = await executeQuery<TicketType>(query, [
        searchPattern,
        searchPattern,
        keyword,
        `${keyword}%`,
        limit
      ]);

      console.log(`✅ 搜索到${results.length}个票种`);
      return results;
    } catch (error) {
      console.error('❌ 搜索票种失败:', error);
      throw new Error(`搜索票种失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 检查票种名称是否已存在
   * @param ticketTypeName 票种名称
   * @param excludeId 排除的票种ID（用于更新时检查）
   * @returns 是否已存在
   */
  async isNameExists(ticketTypeName: string, excludeId?: number): Promise<boolean> {
    try {
      let query = 'SELECT 1 FROM TicketTypes WHERE ticket_type_name = ?';
      const params: any[] = [ticketTypeName];

      if (excludeId) {
        query += ' AND ticket_type_id != ?';
        params.push(excludeId);
      }

      const results = await executeQuery(query, params);
      return results.length > 0;
    } catch (error) {
      console.error('❌ 检查票种名称是否存在失败:', error);
      throw new Error(`检查名称失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取票种使用统计（关联促销活动表）
   * @param ticketTypeId 票种ID
   * @returns 使用统计信息
   */
  async getUsageStatistics(ticketTypeId: number): Promise<{
    promotionCount: number;
    avgPrice: number;
    minPrice: number;
    maxPrice: number;
    latestPromotion?: Date;
  }> {
    try {
      console.log(`📊 获取票种${ticketTypeId}的使用统计`);

      const query = `
        SELECT 
          COUNT(*) as promotionCount,
          AVG(promo_price) as avgPrice,
          MIN(promo_price) as minPrice,
          MAX(promo_price) as maxPrice,
          MAX(entry_date) as latestPromotion
        FROM Promotions 
        WHERE ticket_type_id = ? AND promo_price IS NOT NULL
      `;

      const results = await executeQuery<{
        promotionCount: number;
        avgPrice: number;
        minPrice: number;
        maxPrice: number;
        latestPromotion: Date;
      }>(query, [ticketTypeId]);

      const statistics = results[0] || {
        promotionCount: 0,
        avgPrice: 0,
        minPrice: 0,
        maxPrice: 0,
        latestPromotion: undefined
      };

      console.log('✅ 成功获取票种使用统计:', statistics);
      return statistics;
    } catch (error) {
      console.error('❌ 获取票种使用统计失败:', error);
      throw new Error(`获取使用统计失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取热门票种排行榜
   * @param limit 返回数量限制
   * @returns 热门票种列表
   */
  async getPopularTicketTypes(limit: number = 10): Promise<Array<{
    ticket_type_id: number;
    ticket_type_name: string;
    category: string;
    promotion_count: number;
    avg_price: number;
  }>> {
    try {
      console.log(`📊 获取热门票种排行榜，前${limit}名`);

      const query = `
        SELECT 
          tt.ticket_type_id,
          tt.ticket_type_name,
          tt.category,
          COUNT(p.promotion_id) as promotion_count,
          AVG(p.promo_price) as avg_price
        FROM TicketTypes tt
        LEFT JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id
        GROUP BY tt.ticket_type_id, tt.ticket_type_name, tt.category
        ORDER BY promotion_count DESC, avg_price DESC
        LIMIT ?
      `;

      const results = await executeQuery<{
        ticket_type_id: number;
        ticket_type_name: string;
        category: string;
        promotion_count: number;
        avg_price: number;
      }>(query, [limit]);

      console.log(`✅ 成功获取${results.length}个热门票种`);
      return results;
    } catch (error) {
      console.error('❌ 获取热门票种排行榜失败:', error);
      throw new Error(`获取排行榜失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取票种统计信息
   * @returns 统计信息
   */
  async getStatistics(): Promise<{
    total: number;
    categoryCount: number;
    withPromotions: number;
    withoutPromotions: number;
  }> {
    try {
      console.log('📊 获取票种统计信息');

      const queries = [
        'SELECT COUNT(*) as total FROM TicketTypes',
        'SELECT COUNT(DISTINCT category) as categoryCount FROM TicketTypes WHERE category IS NOT NULL AND category != ""',
        'SELECT COUNT(DISTINCT tt.ticket_type_id) as withPromotions FROM TicketTypes tt INNER JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id',
        'SELECT COUNT(*) as withoutPromotions FROM TicketTypes tt LEFT JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id WHERE p.ticket_type_id IS NULL'
      ];

      const [totalResult, categoryResult, withPromotionsResult, withoutPromotionsResult] = await Promise.all(
        queries.map(query => executeQuery<{ [key: string]: number }>(query))
      );

      const statistics = {
        total: totalResult[0].total,
        categoryCount: categoryResult[0].categoryCount,
        withPromotions: withPromotionsResult[0].withPromotions,
        withoutPromotions: withoutPromotionsResult[0].withoutPromotions
      };

      console.log('✅ 成功获取票种统计信息:', statistics);
      return statistics;
    } catch (error) {
      console.error('❌ 获取票种统计信息失败:', error);
      throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取分类统计信息
   * @returns 分类统计列表
   */
  async getCategoryStatistics(): Promise<Array<{
    category: string;
    ticket_count: number;
    promotion_count: number;
    avg_price: number;
  }>> {
    try {
      console.log('📊 获取分类统计信息');

      const query = `
        SELECT 
          COALESCE(tt.category, '未分类') as category,
          COUNT(DISTINCT tt.ticket_type_id) as ticket_count,
          COUNT(p.promotion_id) as promotion_count,
          AVG(p.promo_price) as avg_price
        FROM TicketTypes tt
        LEFT JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id
        GROUP BY tt.category
        ORDER BY ticket_count DESC, promotion_count DESC
      `;

      const results = await executeQuery<{
        category: string;
        ticket_count: number;
        promotion_count: number;
        avg_price: number;
      }>(query);

      console.log(`✅ 成功获取${results.length}个分类的统计信息`);
      return results;
    } catch (error) {
      console.error('❌ 获取分类统计信息失败:', error);
      throw new Error(`获取分类统计失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

// 导出单例实例
export const ticketTypeDAO = new TicketTypeDAO();
