/**
 * 数据表格组件
 * 
 * 功能说明：
 * 1. 提供完整的数据表格功能
 * 2. 支持排序、筛选、分页
 * 3. 响应式设计，移动端友好
 * 4. 可配置列定义和操作
 */

import React, { useState } from 'react';
import { ChevronUp, ChevronDown, Search, Filter, MoreHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from './button';
import { Input } from './input';
import { Badge } from './badge';
import { LoadingTableRow } from './loading';
import { Pagination } from './pagination';
import { useIsMobile } from '@/lib/responsive-utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from './table';

// ============================================================================
// 表格列定义接口
// ============================================================================

export interface ColumnDef<T> {
  key: string;                    // 列的唯一标识
  title: string;                  // 列标题
  dataIndex?: keyof T;            // 数据字段名
  width?: string | number;        // 列宽度
  sortable?: boolean;             // 是否可排序
  filterable?: boolean;           // 是否可筛选
  searchable?: boolean;           // 是否可搜索
  align?: 'left' | 'center' | 'right'; // 对齐方式
  className?: string;             // 自定义样式
  render?: (value: any, record: T, index: number) => React.ReactNode; // 自定义渲染
  sorter?: (a: T, b: T) => number; // 自定义排序函数
  filters?: Array<{ text: string; value: any }>; // 筛选选项
}

// ============================================================================
// 表格组件接口
// ============================================================================

export interface DataTableProps<T> {
  columns: ColumnDef<T>[];        // 列定义
  data: T[];                      // 数据源
  loading?: boolean;              // 加载状态
  pagination?: {                  // 分页配置
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    onChange: (page: number, pageSize: number) => void;
  };
  rowKey?: keyof T | ((record: T) => string); // 行唯一标识
  onRow?: (record: T, index: number) => {     // 行事件处理
    onClick?: () => void;
    onDoubleClick?: () => void;
    className?: string;
  };
  emptyText?: string;             // 空数据提示
  size?: 'small' | 'middle' | 'large'; // 表格尺寸
  bordered?: boolean;             // 是否显示边框
  striped?: boolean;              // 是否显示斑马纹
  className?: string;
}

// ============================================================================
// 数据表格组件实现
// ============================================================================

export function DataTable<T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  pagination,
  rowKey = 'id',
  onRow,
  emptyText = '暂无数据',
  className
}: DataTableProps<T>) {
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState<Record<string, any>>({});
  const isMobile = useIsMobile();

  // 获取行的唯一标识
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return record[rowKey]?.toString() || index.toString();
  };

  // 处理排序
  const handleSort = (column: ColumnDef<T>) => {
    if (!column.sortable) return;

    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig?.key === column.key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }

    setSortConfig({ key: column.key, direction });
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 处理筛选
  const handleFilter = (columnKey: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [columnKey]: value
    }));
  };

  // 渲染排序图标
  const renderSortIcon = (column: ColumnDef<T>) => {
    if (!column.sortable) return null;

    const isActive = sortConfig?.key === column.key;
    const direction = sortConfig?.direction;

    return (
      <span className="ml-1 inline-flex flex-col">
        <ChevronUp 
          className={cn(
            'h-3 w-3 -mb-1',
            isActive && direction === 'asc' ? 'text-blue-600' : 'text-gray-400'
          )} 
        />
        <ChevronDown 
          className={cn(
            'h-3 w-3',
            isActive && direction === 'desc' ? 'text-blue-600' : 'text-gray-400'
          )} 
        />
      </span>
    );
  };

  // 渲染表头
  const renderHeader = () => (
    <TableHeader>
      <TableRow>
        {columns.map((column) => (
          <TableHead
            key={column.key}
            className={cn(
              column.className,
              column.sortable && 'cursor-pointer hover:bg-gray-50',
              column.align === 'center' && 'text-center',
              column.align === 'right' && 'text-right'
            )}
            style={{ width: column.width }}
            onClick={() => handleSort(column)}
          >
            <div className="flex items-center">
              {column.title}
              {renderSortIcon(column)}
            </div>
          </TableHead>
        ))}
      </TableRow>
    </TableHeader>
  );

  // 渲染单元格内容
  const renderCell = (column: ColumnDef<T>, record: T, index: number) => {
    const value = column.dataIndex ? record[column.dataIndex] : undefined;
    
    if (column.render) {
      return column.render(value, record, index);
    }

    // 默认渲染逻辑
    if (value === null || value === undefined) {
      return <span className="text-gray-400">-</span>;
    }

    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? '是' : '否'}
        </Badge>
      );
    }

    if (typeof value === 'number') {
      return value.toLocaleString();
    }

    return value.toString();
  };

  // 渲染表格主体
  const renderBody = () => {
    if (loading) {
      return (
        <TableBody>
          <LoadingTableRow columns={columns.length} rows={5} />
        </TableBody>
      );
    }

    if (data.length === 0) {
      return (
        <TableBody>
          <TableRow>
            <TableCell 
              colSpan={columns.length} 
              className="text-center py-8 text-gray-500"
            >
              {emptyText}
            </TableCell>
          </TableRow>
        </TableBody>
      );
    }

    return (
      <TableBody>
        {data.map((record, index) => {
          const rowProps = onRow?.(record, index) || {};
          
          return (
            <TableRow
              key={getRowKey(record, index)}
              className={cn(
                rowProps.className,
                rowProps.onClick && 'cursor-pointer hover:bg-gray-50',
                index % 2 === 1 && 'bg-gray-50/50'
              )}
              onClick={rowProps.onClick}
              onDoubleClick={rowProps.onDoubleClick}
            >
              {columns.map((column) => (
                <TableCell
                  key={column.key}
                  className={cn(
                    column.align === 'center' && 'text-center',
                    column.align === 'right' && 'text-right'
                  )}
                >
                  {renderCell(column, record, index)}
                </TableCell>
              ))}
            </TableRow>
          );
        })}
      </TableBody>
    );
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* 表格工具栏 */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex gap-2">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索..."
              value={searchText}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9 w-64"
            />
          </div>

          {/* 筛选按钮 */}
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <MoreHorizontal className="h-4 w-4 mr-2" />
            更多操作
          </Button>
        </div>
      </div>

      {/* 表格 */}
      <div className="rounded-md border">
        <Table>
          {renderHeader()}
          {renderBody()}
        </Table>
      </div>

      {/* 分页 */}
      {pagination && (
        <Pagination
          currentPage={pagination.current}
          totalPages={Math.ceil(pagination.total / pagination.pageSize)}
          totalItems={pagination.total}
          pageSize={pagination.pageSize}
          onPageChange={(page) => pagination.onChange(page, pagination.pageSize)}
          onPageSizeChange={(pageSize) => pagination.onChange(1, pageSize)}
          showPageSizeSelector={pagination.showSizeChanger}
          showQuickJumper={pagination.showQuickJumper}
        />
      )}
    </div>
  );
}

// ============================================================================
// 导出组件
// ============================================================================

export { DataTable as default };
