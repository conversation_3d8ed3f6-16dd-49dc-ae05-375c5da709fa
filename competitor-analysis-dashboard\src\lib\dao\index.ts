/**
 * 数据访问对象(DAO)统一导出文件
 * 
 * 功能说明：
 * 1. 统一导出所有DAO类和实例
 * 2. 提供DAO的中央管理
 * 3. 方便其他模块引用和使用
 */

// ============================================================================
// 导出基础DAO类
// ============================================================================

export { BaseDAO } from './base-dao';

// ============================================================================
// 导出具体DAO类和实例
// ============================================================================

// 竞品DAO
export { CompetitorDAO, competitorDAO } from './competitor-dao';

// 票种DAO
export { TicketTypeDAO, ticketTypeDAO } from './ticket-type-dao';

// 促销活动DAO
export { PromotionDAO, promotionDAO } from './promotion-dao';

// 使用规则DAO
export { UsageRuleDAO, usageRuleDAO } from './usage-rule-dao';

// 导入DAO实例
import { competitorDAO } from './competitor-dao';
import { ticketTypeDAO } from './ticket-type-dao';
import { promotionDAO } from './promotion-dao';
import { usageRuleDAO } from './usage-rule-dao';

// ============================================================================
// 创建DAO管理器类
// ============================================================================

/**
 * DAO管理器类
 * 提供统一的DAO访问接口
 */
export class DAOManager {
  // DAO实例 - 使用getter延迟初始化
  public get competitor() {
    return competitorDAO;
  }

  public get ticketType() {
    return ticketTypeDAO;
  }

  public get promotion() {
    return promotionDAO;
  }

  public get usageRule() {
    return usageRuleDAO;
  }

  /**
   * 获取所有DAO实例
   * @returns DAO实例对象
   */
  getAllDAOs() {
    return {
      competitor: this.competitor,
      ticketType: this.ticketType,
      promotion: this.promotion,
      usageRule: this.usageRule
    };
  }

  /**
   * 检查所有DAO的数据库连接
   * @returns 连接状态
   */
  async checkConnections(): Promise<{
    competitor: boolean;
    ticketType: boolean;
    promotion: boolean;
    usageRule: boolean;
    allConnected: boolean;
  }> {
    try {
      console.log('🔍 检查所有DAO的数据库连接状态');

      const [competitorCheck, ticketTypeCheck, promotionCheck, usageRuleCheck] = await Promise.all([
        this.competitor.exists(1).then(() => true).catch(() => false),
        this.ticketType.exists(1).then(() => true).catch(() => false),
        this.promotion.exists(1).then(() => true).catch(() => false),
        this.usageRule.exists(1).then(() => true).catch(() => false)
      ]);

      const result = {
        competitor: competitorCheck,
        ticketType: ticketTypeCheck,
        promotion: promotionCheck,
        usageRule: usageRuleCheck,
        allConnected: competitorCheck && ticketTypeCheck && promotionCheck && usageRuleCheck
      };

      console.log('✅ DAO连接检查完成:', result);
      return result;
    } catch (error) {
      console.error('❌ DAO连接检查失败:', error);
      return {
        competitor: false,
        ticketType: false,
        promotion: false,
        usageRule: false,
        allConnected: false
      };
    }
  }

  /**
   * 获取系统整体统计信息
   * @returns 统计信息
   */
  async getSystemStatistics(): Promise<{
    competitors: any;
    ticketTypes: any;
    promotions: any;
    summary: {
      totalRecords: number;
      lastUpdated: string;
    };
  }> {
    try {
      console.log('📊 获取系统整体统计信息');

      const [competitorStats, ticketTypeStats, promotionStats] = await Promise.all([
        this.competitor.getStatistics(),
        this.ticketType.getStatistics(),
        this.promotion.getStatistics()
      ]);

      const totalRecords = competitorStats.total + ticketTypeStats.total + promotionStats.total;

      const result = {
        competitors: competitorStats,
        ticketTypes: ticketTypeStats,
        promotions: promotionStats,
        summary: {
          totalRecords,
          lastUpdated: new Date().toISOString()
        }
      };

      console.log('✅ 成功获取系统统计信息');
      return result;
    } catch (error) {
      console.error('❌ 获取系统统计信息失败:', error);
      throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 清理和优化所有表
   * @returns 清理结果
   */
  async optimizeTables(): Promise<{
    success: boolean;
    message: string;
    details: string[];
  }> {
    try {
      console.log('🔧 开始优化数据库表');

      const details: string[] = [];

      // 这里可以添加具体的优化操作
      // 例如：清理无效数据、重建索引等
      details.push('数据库表优化功能待实现');

      console.log('✅ 数据库表优化完成');

      return {
        success: true,
        message: '数据库表优化完成',
        details
      };
    } catch (error) {
      console.error('❌ 数据库表优化失败:', error);
      return {
        success: false,
        message: `优化失败: ${error instanceof Error ? error.message : '未知错误'}`,
        details: []
      };
    }
  }
}

// ============================================================================
// 导出DAO管理器单例实例
// ============================================================================

export const daoManager = new DAOManager();

// ============================================================================
// 导出常用的DAO操作函数
// ============================================================================

/**
 * 快速获取竞品列表
 * @param filters 筛选条件
 * @param pagination 分页参数
 * @returns 竞品列表
 */
export const getCompetitors = (filters?: any, pagination?: any) => {
  return competitorDAO.findWithFilters(pagination || { page: 1, pageSize: 10 }, filters);
};

/**
 * 快速获取票种列表
 * @param filters 筛选条件
 * @param pagination 分页参数
 * @returns 票种列表
 */
export const getTicketTypes = (filters?: any, pagination?: any) => {
  return ticketTypeDAO.findWithFilters(pagination || { page: 1, pageSize: 10 }, filters);
};

/**
 * 快速获取促销活动列表
 * @param filters 筛选条件
 * @param pagination 分页参数
 * @returns 促销活动列表
 */
export const getPromotions = (filters?: any, pagination?: any) => {
  return promotionDAO.findWithFilters(pagination || { page: 1, pageSize: 10 }, filters);
};

/**
 * 快速创建竞品
 * @param data 竞品数据
 * @returns 创建结果
 */
export const createCompetitor = (data: any) => {
  return competitorDAO.create(data);
};

/**
 * 快速创建票种
 * @param data 票种数据
 * @returns 创建结果
 */
export const createTicketType = (data: any) => {
  return ticketTypeDAO.create(data);
};

/**
 * 快速创建促销活动
 * @param data 促销活动数据
 * @returns 创建结果
 */
export const createPromotion = (data: any) => {
  return promotionDAO.create(data);
};

// ============================================================================
// 导出默认实例（向后兼容）
// ============================================================================

export default daoManager;
