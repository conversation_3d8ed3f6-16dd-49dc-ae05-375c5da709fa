# 促销活动批量导入功能使用指南

## 功能概述

促销活动批量导入功能允许用户通过上传标准格式的Excel文件来批量创建促销活动，大大提高数据录入效率。

## 功能特性

### 1. Excel模板下载
- 提供标准的Excel模板文件
- 包含所有必需的列标题和数据格式说明
- 提供示例数据帮助用户理解格式
- 包含详细的填写说明和注意事项

### 2. 文件上传与验证
- 支持 `.xlsx` 和 `.xls` 格式
- 文件大小限制：最大10MB
- 最大导入行数：1000条记录
- 自动验证文件格式和结构

### 3. 数据验证
- **必填字段验证**：竞品名称、票种名称、活动名称
- **数据类型验证**：价格字段必须为数字，日期字段必须为YYYY-MM-DD格式
- **业务规则验证**：
  - 竞品名称和票种名称必须在系统中已存在
  - 价格字段不能为负数
  - 日期格式必须正确且真实存在

### 4. 导入结果反馈
- 显示导入进度条
- 提供详细的导入统计：总计、成功、失败条数
- 对于失败的记录，提供具体的错误信息和行号
- 支持部分成功导入（跳过错误行，导入正确数据）

## 使用步骤

### 第一步：下载Excel模板
1. 在促销活动管理页面点击"批量导入"按钮
2. 在弹出的对话框中点击"下载模板"按钮
3. 保存模板文件到本地

### 第二步：填写数据
1. 打开下载的Excel模板文件
2. 在"促销活动导入模板"工作表中填写数据
3. 删除示例数据行和说明行，只保留标题行和实际数据行
4. 确保数据格式符合要求

### 第三步：上传导入
1. 在批量导入对话框中点击"选择文件"
2. 选择填写好的Excel文件
3. 点击"开始导入"按钮
4. 等待导入完成，查看导入结果

## Excel模板字段说明

| 字段名 | 是否必填 | 数据类型 | 示例值 | 说明 |
|--------|----------|----------|--------|------|
| 竞品名称 | 是 | 文本 | 上海迪士尼乐园 | 必须是系统中已存在的竞品 |
| 票种名称 | 是 | 文本 | 成人票 | 必须是系统中已存在的票种 |
| 活动名称 | 是 | 文本 | 春节特惠活动 | 促销活动的名称 |
| 门市价 | 否 | 数字 | 399 | 原价格，不要包含货币符号 |
| 促销价 | 否 | 数字 | 299 | 促销价格，不要包含货币符号 |
| 销售开始日期 | 否 | 日期 | 2024-01-01 | 格式：YYYY-MM-DD |
| 销售结束日期 | 否 | 日期 | 2024-12-31 | 格式：YYYY-MM-DD |
| 使用开始日期 | 否 | 日期 | 2024-01-01 | 格式：YYYY-MM-DD |
| 使用结束日期 | 否 | 日期 | 2024-12-31 | 格式：YYYY-MM-DD |
| 销售渠道 | 否 | 文本 | 官网,携程,美团 | 多个渠道用逗号分隔 |
| 使用规则 | 否 | 文本 | 限周末使用，需提前预约 | 使用限制和规则说明 |
| 数据来源URL | 否 | 文本 | https://example.com/promotion | 数据来源的网址 |
| 备注 | 否 | 文本 | 春节期间特别优惠 | 其他备注信息 |

## 常见错误及解决方法

### 1. 文件格式错误
- **错误信息**：不支持的文件格式
- **解决方法**：确保上传的是 `.xlsx` 或 `.xls` 格式的Excel文件

### 2. 必填字段为空
- **错误信息**：竞品名称不能为空 / 票种名称不能为空 / 活动名称不能为空
- **解决方法**：检查对应行的必填字段是否已填写

### 3. 竞品或票种不存在
- **错误信息**：竞品"XXX"不存在 / 票种"XXX"不存在
- **解决方法**：
  - 检查竞品名称和票种名称的拼写是否正确
  - 确认该竞品和票种在系统中已经创建
  - 注意名称的大小写和空格

### 4. 数据格式错误
- **错误信息**：门市价必须是非负数字 / 日期格式不正确
- **解决方法**：
  - 价格字段只填写数字，不要包含货币符号
  - 日期格式必须为 YYYY-MM-DD（如：2024-01-01）
  - 确保日期是真实存在的

### 5. 文件过大或数据过多
- **错误信息**：文件大小超过限制 / 导入数据超过限制
- **解决方法**：
  - 文件大小不要超过10MB
  - 单次导入不要超过1000条记录
  - 可以分批导入大量数据

## 注意事项

1. **数据准备**：
   - 建议先在系统中创建好所需的竞品和票种
   - 确保Excel文件中的竞品名称和票种名称与系统中的完全一致

2. **数据格式**：
   - 不要修改模板中的列标题
   - 日期格式必须严格按照 YYYY-MM-DD 格式
   - 价格字段不要包含货币符号或其他字符

3. **导入策略**：
   - 建议先用少量数据测试导入功能
   - 对于大量数据，建议分批导入
   - 导入前备份重要数据

4. **错误处理**：
   - 仔细查看错误报告，根据行号定位问题
   - 修正错误后可以重新导入
   - 系统支持部分成功导入，正确的数据会被保存

## 技术实现

### API端点
- **模板下载**：`GET /api/promotions/template`
- **批量导入**：`POST /api/promotions/bulk-import`

### 数据处理流程
1. 文件上传和格式验证
2. Excel数据解析和结构验证
3. 业务规则验证和数据转换
4. 批量数据库操作
5. 结果统计和错误报告

### 错误处理机制
- 逐行验证，单行错误不影响其他行
- 详细的错误信息包含行号和具体原因
- 事务处理确保数据一致性

## 更新日志

### v1.0.0 (2024-01-19)
- 初始版本发布
- 支持Excel文件批量导入
- 提供标准模板下载
- 完整的数据验证和错误处理
- 导入进度显示和结果反馈
