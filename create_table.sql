-- 创建一个名为 'competitor_analysis_db' 的数据库
CREATE DATABASE competitor_analysis_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 切换到这个新创建的数据库，后续所有操作都将在这个数据库中进行
USE competitor_analysis_db;
-- =================================================================
-- 表1: 竞品字典表 (Competitors)
-- 作用: 统一管理所有竞品信息
-- =================================================================
CREATE TABLE Competitors (
    competitor_id   INT PRIMARY KEY AUTO_INCREMENT,
    competitor_name VARCHAR(100) NOT NULL,
    city            VARCHAR(50),
    park_type       VARCHAR(50),
    is_active       BOOLEAN DEFAULT TRUE,
    INDEX (competitor_name) -- 为竞品名称添加索引，加快查询速度
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- =================================================================
-- 表2: 票种字典表 (TicketTypes)
-- 作用: 统一管理所有票种信息
-- =================================================================
CREATE TABLE TicketTypes (
    ticket_type_id   INT PRIMARY KEY AUTO_INCREMENT,
    ticket_type_name VARCHAR(100) NOT NULL,
    category         VARCHAR(50),
    INDEX (ticket_type_name) -- 为票种名称添加索引
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- =================================================================
-- 表3: 竞品促销活动主表 (Promotions)
-- 作用: 记录每一条具体的促销活动，这是数据录入的核心
-- =================================================================
CREATE TABLE Promotions (
    promotion_id      INT PRIMARY KEY AUTO_INCREMENT,
    competitor_id     INT NOT NULL,
    ticket_type_id    INT NOT NULL,
    activity_name     VARCHAR(255) NOT NULL,
    rack_rate         DECIMAL(10, 2) COMMENT '门市价',
    promo_price       DECIMAL(10, 2) COMMENT '促销价',
    sale_start_date   DATE,
    sale_end_date     DATE,
    use_start_date    DATE,
    use_end_date      DATE,
    sales_channel     VARCHAR(255),
    usage_rules       TEXT,
    data_source_url   VARCHAR(512),
    entry_date        DATETIME DEFAULT CURRENT_TIMESTAMP,
    remarks           TEXT,

    -- 创建外键约束，确保数据完整性
    CONSTRAINT fk_promo_competitor
        FOREIGN KEY (competitor_id) REFERENCES Competitors(competitor_id),
    
    CONSTRAINT fk_promo_ticket_type
        FOREIGN KEY (ticket_type_id) REFERENCES TicketTypes(ticket_type_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- =================================================================
-- 表4: 促销日历-销售维度 (DailySales)
-- 作用: 将促销的销售期展开为每日数据
-- =================================================================
CREATE TABLE DailySales (
    sales_date      DATE NOT NULL,
    promotion_id    INT NOT NULL,
    promo_price     DECIMAL(10, 2),
    rack_rate       DECIMAL(10, 2),
    discount_rate   DECIMAL(5, 4),
    
    -- 复合主键：销售日期和促销ID的组合是唯一的
    PRIMARY KEY (sales_date, promotion_id),
    
    -- 外键关联到Promotions表
    CONSTRAINT fk_sales_promotion
        FOREIGN KEY (promotion_id) REFERENCES Promotions(promotion_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- =================================================================
-- 表5: 促销日历-核销维度 (DailyUsage)
-- 作用: 将促销的使用期展开为每日数据
-- =================================================================
CREATE TABLE DailyUsage (
    usage_date      DATE NOT NULL,
    promotion_id    INT NOT NULL,
    promo_price     DECIMAL(10, 2),
    rack_rate       DECIMAL(10, 2),
    discount_rate   DECIMAL(5, 4),

    -- 复合主键：使用日期和促销ID的组合是唯一的
    PRIMARY KEY (usage_date, promotion_id),

    -- 外键关联到Promotions表
    CONSTRAINT fk_usage_promotion
        FOREIGN KEY (promotion_id) REFERENCES Promotions(promotion_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- =================================================================
-- 表6: 使用规则字典表 (UsageRules)
-- 作用: 统一管理所有票种的使用规则
-- =================================================================
CREATE TABLE UsageRules (
    rule_id          INT PRIMARY KEY AUTO_INCREMENT,
    rule_description VARCHAR(255) NOT NULL UNIQUE COMMENT '规则的文字描述',
    is_active        BOOLEAN DEFAULT TRUE COMMENT '该规则是否仍在使用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- 向 UsageRules 表中插入一些初始数据
INSERT INTO UsageRules (rule_description) VALUES
('无限制'),
('仅限平日使用'),
('仅限周末使用'),
('周末及法定节假日不可用'),
('需至少提前1天预约');