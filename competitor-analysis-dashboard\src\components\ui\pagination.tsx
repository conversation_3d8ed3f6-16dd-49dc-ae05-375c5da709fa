/**
 * 分页组件
 * 
 * 功能说明：
 * 1. 提供完整的分页功能
 * 2. 支持页码跳转和页面大小选择
 * 3. 响应式设计，适配移动端
 * 4. 可配置显示样式和行为
 */

import React from 'react';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from './button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';

// ============================================================================
// 分页组件接口定义
// ============================================================================

export interface PaginationProps {
  currentPage: number;          // 当前页码
  totalPages: number;           // 总页数
  totalItems: number;           // 总记录数
  pageSize: number;             // 每页记录数
  onPageChange: (page: number) => void;        // 页码变更回调
  onPageSizeChange?: (pageSize: number) => void; // 页面大小变更回调
  showPageSizeSelector?: boolean;               // 是否显示页面大小选择器
  pageSizeOptions?: number[];                   // 页面大小选项
  showTotal?: boolean;                          // 是否显示总数信息
  showQuickJumper?: boolean;                    // 是否显示快速跳转
  className?: string;
}

// ============================================================================
// 分页组件实现
// ============================================================================

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  onPageChange,
  onPageSizeChange,
  showPageSizeSelector = true,
  pageSizeOptions = [10, 20, 50, 100],
  showTotal = true,
  showQuickJumper = false,
  className
}) => {
  // 计算显示的页码范围
  const getVisiblePages = (): (number | 'ellipsis')[] => {
    const delta = 2; // 当前页前后显示的页数
    const range: (number | 'ellipsis')[] = [];
    const rangeWithDots: (number | 'ellipsis')[] = [];

    // 总是显示第一页
    range.push(1);

    // 计算当前页周围的页码
    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    // 总是显示最后一页（如果总页数大于1）
    if (totalPages > 1) {
      range.push(totalPages);
    }

    // 添加省略号
    let prev = 0;
    for (const page of range) {
      if (typeof page === 'number') {
        if (page - prev === 2) {
          rangeWithDots.push(prev + 1);
        } else if (page - prev !== 1) {
          rangeWithDots.push('ellipsis');
        }
        rangeWithDots.push(page);
        prev = page;
      }
    }

    return rangeWithDots;
  };

  const visiblePages = getVisiblePages();

  // 计算当前页显示的记录范围
  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, totalItems);

  // 快速跳转处理
  const handleQuickJump = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      const target = event.target as HTMLInputElement;
      const page = parseInt(target.value);
      if (page >= 1 && page <= totalPages) {
        onPageChange(page);
        target.value = '';
      }
    }
  };

  return (
    <div className={cn('flex flex-col sm:flex-row items-center justify-between gap-4', className)}>
      {/* 总数信息 */}
      {showTotal && (
        <div className="text-sm text-gray-700 order-2 sm:order-1">
          显示第 <span className="font-medium">{startItem}</span> 到{' '}
          <span className="font-medium">{endItem}</span> 条，共{' '}
          <span className="font-medium">{totalItems}</span> 条记录
        </div>
      )}

      {/* 分页控件 */}
      <div className="flex items-center gap-2 order-1 sm:order-2">
        {/* 页面大小选择器 */}
        {showPageSizeSelector && onPageSizeChange && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700 whitespace-nowrap">每页</span>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => onPageSizeChange(parseInt(value))}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <span className="text-sm text-gray-700 whitespace-nowrap">条</span>
          </div>
        )}

        {/* 页码导航 */}
        <div className="flex items-center gap-1">
          {/* 上一页按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          {/* 页码按钮 */}
          {visiblePages.map((page, index) => {
            if (page === 'ellipsis') {
              return (
                <div key={`ellipsis-${index}`} className="flex h-8 w-8 items-center justify-center">
                  <MoreHorizontal className="h-4 w-4" />
                </div>
              );
            }

            return (
              <Button
                key={page}
                variant={page === currentPage ? 'default' : 'outline'}
                size="sm"
                onClick={() => onPageChange(page)}
                className="h-8 w-8 p-0"
              >
                {page}
              </Button>
            );
          })}

          {/* 下一页按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* 快速跳转 */}
        {showQuickJumper && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700 whitespace-nowrap">跳转到</span>
            <input
              type="number"
              min={1}
              max={totalPages}
              placeholder="页码"
              onKeyDown={handleQuickJump}
              className="w-16 h-8 px-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700 whitespace-nowrap">页</span>
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// 简化版分页组件
// ============================================================================

export interface SimplePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export const SimplePagination: React.FC<SimplePaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  className
}) => {
  return (
    <div className={cn('flex items-center justify-center gap-2', className)}>
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage <= 1}
      >
        <ChevronLeft className="h-4 w-4 mr-1" />
        上一页
      </Button>

      <span className="text-sm text-gray-700 px-4">
        第 {currentPage} 页，共 {totalPages} 页
      </span>

      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage >= totalPages}
      >
        下一页
        <ChevronRight className="h-4 w-4 ml-1" />
      </Button>
    </div>
  );
};

// ============================================================================
// 移动端分页组件
// ============================================================================

export interface MobilePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export const MobilePagination: React.FC<MobilePaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  className
}) => {
  return (
    <div className={cn('flex flex-col gap-3', className)}>
      {/* 页码信息 */}
      <div className="text-center text-sm text-gray-700">
        第 {currentPage} 页，共 {totalPages} 页
      </div>

      {/* 导航按钮 */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className="flex-1"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          上一页
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
          className="flex-1"
        >
          下一页
          <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </div>
    </div>
  );
};

// ============================================================================
// 导出所有组件
// ============================================================================

export {
  Pagination as default,
  SimplePagination,
  MobilePagination
};
