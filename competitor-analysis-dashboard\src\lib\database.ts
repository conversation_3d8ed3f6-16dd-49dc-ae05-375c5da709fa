/**
 * 数据库连接配置和连接池管理
 * 
 * 功能说明：
 * 1. 创建MySQL连接池，支持高并发访问
 * 2. 提供数据库连接的获取和释放机制
 * 3. 实现连接错误处理和重连机制
 * 4. 提供数据库健康检查功能
 */

import mysql from 'mysql2/promise';

// 数据库连接配置接口定义
interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  connectionLimit: number;
  queueLimit: number;
  timeout: number;
  acquireTimeout: number;
}

// 从环境变量获取数据库配置
const getDatabaseConfig = (): DatabaseConfig => {
  const requiredEnvVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
  
  // 检查必需的环境变量
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`缺少必需的环境变量: ${envVar}`);
    }
  }

  return {
    host: process.env.DB_HOST!,
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER!,
    password: process.env.DB_PASSWORD!,
    database: process.env.DB_NAME!,
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10'),
    queueLimit: parseInt(process.env.DB_QUEUE_LIMIT || '0'),
    timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
    acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
  };
};

// 全局连接池变量
let connectionPool: mysql.Pool | null = null;

/**
 * 创建数据库连接池
 * 使用单例模式确保整个应用只有一个连接池实例
 */
export const createConnectionPool = (): mysql.Pool => {
  if (connectionPool) {
    return connectionPool;
  }

  try {
    const config = getDatabaseConfig();
    
    console.log('🔄 正在创建数据库连接池...');
    console.log(`📍 数据库地址: ${config.host}:${config.port}`);
    console.log(`🗄️  数据库名称: ${config.database}`);
    console.log(`👤 用户名: ${config.user}`);
    console.log(`🔗 连接池大小: ${config.connectionLimit}`);

    connectionPool = mysql.createPool({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
      database: config.database,
      waitForConnections: true,
      connectionLimit: config.connectionLimit,
      queueLimit: config.queueLimit,
      // 启用多语句查询
      multipleStatements: false,
      // 字符集设置
      charset: 'utf8mb4',
      // 时区设置
      timezone: '+08:00',
      // 连接保活设置
      keepAliveInitialDelay: 0,
      enableKeepAlive: true,
    });

    console.log('✅ 数据库连接池创建成功');
    return connectionPool;
  } catch (error) {
    console.error('❌ 创建数据库连接池失败:', error);
    throw new Error(`数据库连接池创建失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

/**
 * 获取数据库连接池实例
 */
export const getConnectionPool = (): mysql.Pool => {
  if (!connectionPool) {
    return createConnectionPool();
  }
  return connectionPool;
};

/**
 * 执行数据库查询
 * @param query SQL查询语句
 * @param params 查询参数
 * @returns 查询结果
 */
export const executeQuery = async <T = Record<string, unknown>>(
  query: string,
  params: unknown[] = []
): Promise<T[]> => {
  const pool = getConnectionPool();
  
  try {
    console.log('🔍 执行SQL查询:', query);
    // 将 undefined 统一转换为 null，避免 mysql2 拒绝绑定 undefined 参数
    const safeParams: unknown[] = Array.isArray(params)
      ? params.map((param) => (param === undefined ? null : param))
      : [];
    console.log('📝 查询参数:', safeParams);
    
    const [rows] = await pool.execute(query, safeParams);
    
    console.log('✅ 查询执行成功');
    return rows as T[];
  } catch (error) {
    console.error('❌ 数据库查询失败:', error);
    console.error('🔍 失败的SQL:', query);
    console.error('📝 查询参数:', Array.isArray(params) ? params.map((p) => (p === undefined ? null : p)) : []);
    
    throw new Error(`数据库查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

/**
 * 执行数据库事务
 * @param callback 事务回调函数
 * @returns 事务执行结果
 */
export const executeTransaction = async <T>(
  callback: (connection: mysql.PoolConnection) => Promise<T>
): Promise<T> => {
  const pool = getConnectionPool();
  const connection = await pool.getConnection();
  
  try {
    console.log('🔄 开始数据库事务');
    await connection.beginTransaction();
    
    const result = await callback(connection);
    
    await connection.commit();
    console.log('✅ 事务提交成功');
    
    return result;
  } catch (error) {
    console.error('❌ 事务执行失败，正在回滚:', error);
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
    console.log('🔄 数据库连接已释放');
  }
};

/**
 * 检查数据库连接健康状态
 */
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    console.log('🏥 检查数据库连接健康状态...');
    
    const result = await executeQuery('SELECT 1 as health_check');
    
    if (result && result.length > 0) {
      console.log('✅ 数据库连接健康');
      return true;
    } else {
      console.log('⚠️ 数据库连接异常');
      return false;
    }
  } catch (error) {
    console.error('❌ 数据库健康检查失败:', error);
    return false;
  }
};

/**
 * 关闭数据库连接池
 * 通常在应用程序关闭时调用
 */
export const closeDatabasePool = async (): Promise<void> => {
  if (connectionPool) {
    try {
      console.log('🔄 正在关闭数据库连接池...');
      await connectionPool.end();
      connectionPool = null;
      console.log('✅ 数据库连接池已关闭');
    } catch (error) {
      console.error('❌ 关闭数据库连接池失败:', error);
      throw error;
    }
  }
};

// 导出默认连接池实例
export default getConnectionPool;
