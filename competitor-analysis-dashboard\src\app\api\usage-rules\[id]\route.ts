/**
 * 使用规则API动态路由 - 单个规则操作
 * 
 * 功能说明：
 * 1. 支持根据ID获取单个规则
 * 2. 支持更新规则信息
 * 3. 支持删除规则（软删除）
 */

import { NextRequest, NextResponse } from 'next/server';
import { usageRuleDAO } from '@/lib/dao';
import { UpdateUsageRuleInput } from '@/types';
import { 
  withErrorHandling, 
  parseRequestBody, 
  createSuccessResponse,
  createValidationErrorResponse,
  validateStringLength
} from '@/lib/api-utils';

// ============================================================================
// GET /api/usage-rules/[id]
// 获取单个使用规则
// ============================================================================

export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  console.log(`📥 GET /api/usage-rules/${params.id} - 获取使用规则详情`);

  const ruleId = parseInt(params.id);
  if (isNaN(ruleId)) {
    return createValidationErrorResponse('无效的规则ID');
  }

  try {
    const rule = await usageRuleDAO.findById(ruleId);
    
    if (!rule) {
      return createValidationErrorResponse('使用规则不存在');
    }

    console.log(`✅ 成功获取使用规则详情，ID: ${ruleId}`);
    return createSuccessResponse(rule, '获取使用规则详情成功');
  } catch (error) {
    console.error('❌ 获取使用规则详情失败:', error);
    throw error;
  }
});

// ============================================================================
// PUT /api/usage-rules/[id]
// 更新使用规则
// ============================================================================

export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  console.log(`📥 PUT /api/usage-rules/${params.id} - 更新使用规则`);

  const ruleId = parseInt(params.id);
  if (isNaN(ruleId)) {
    return createValidationErrorResponse('无效的规则ID');
  }

  // 解析请求体
  const body = await parseRequestBody<UpdateUsageRuleInput>(request);
  console.log(`📝 更新规则ID ${ruleId}，数据:`, body);

  // 检查规则是否存在
  const existingRule = await usageRuleDAO.findById(ruleId);
  if (!existingRule) {
    return createValidationErrorResponse('使用规则不存在');
  }

  // 验证字段格式和长度
  if (body.rule_description !== undefined) {
    if (!validateStringLength(body.rule_description, 1, 255)) {
      return createValidationErrorResponse('规则描述长度必须在1-255个字符之间');
    }

    // 检查规则描述是否已存在（排除当前规则）
    const exists = await usageRuleDAO.isDescriptionExists(body.rule_description, ruleId);
    if (exists) {
      return createValidationErrorResponse('该规则描述已存在');
    }
  }

  // 更新使用规则
  await usageRuleDAO.update(ruleId, body);

  // 获取更新后的使用规则详情
  const updatedRule = await usageRuleDAO.findById(ruleId);

  console.log(`✅ 成功更新使用规则，ID: ${ruleId}`);
  return createSuccessResponse(updatedRule, '更新使用规则成功');
});

// ============================================================================
// DELETE /api/usage-rules/[id]
// 删除使用规则（软删除，设置为不活跃）
// ============================================================================

export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  console.log(`📥 DELETE /api/usage-rules/${params.id} - 删除使用规则`);

  const ruleId = parseInt(params.id);
  if (isNaN(ruleId)) {
    return createValidationErrorResponse('无效的规则ID');
  }

  console.log(`🗑️ 删除规则ID: ${ruleId}`);

  // 检查规则是否存在
  const existingRule = await usageRuleDAO.findById(ruleId);
  if (!existingRule) {
    return createValidationErrorResponse('使用规则不存在');
  }

  // 禁止删除默认规则（ID为1的"无限制"规则）
  if (ruleId === 1) {
    return createValidationErrorResponse('不能删除默认的"无限制"规则');
  }

  // 软删除：设置为不活跃
  await usageRuleDAO.update(ruleId, { is_active: false });

  console.log(`✅ 成功删除使用规则，ID: ${ruleId}`);
  return createSuccessResponse(null, '删除使用规则成功');
});
