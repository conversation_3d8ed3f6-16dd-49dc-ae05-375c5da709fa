# 竞品表单调试测试脚本
# 
# 功能说明：
# 1. 启动开发服务器
# 2. 打开调试页面
# 3. 提供调试指导

Write-Host "🚀 开始竞品表单调试测试..." -ForegroundColor Green

# 检查是否在正确的目录
if (-not (Test-Path "package.json")) {
    Write-Host "❌ 错误：请在项目根目录运行此脚本" -ForegroundColor Red
    exit 1
}

# 检查Node.js和npm
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
    Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误：未找到Node.js或npm，请先安装" -ForegroundColor Red
    exit 1
}

# 安装依赖（如果需要）
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 安装项目依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 依赖安装失败" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "🔧 调试步骤说明：" -ForegroundColor Cyan
Write-Host "1. 开发服务器将在 http://localhost:3000 启动" -ForegroundColor White
Write-Host "2. 调试页面将在 http://localhost:3000/debug-competitor-form.html 打开" -ForegroundColor White
Write-Host "3. 请按照调试页面的指导进行测试" -ForegroundColor White
Write-Host ""

# 启动开发服务器
Write-Host "🌐 启动开发服务器..." -ForegroundColor Yellow
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Gray

# 在后台启动服务器
$job = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    npm run dev
}

# 等待服务器启动
Write-Host "⏳ 等待服务器启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# 检查服务器是否启动成功
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ 服务器启动成功！" -ForegroundColor Green
} catch {
    Write-Host "⚠️  服务器可能还在启动中，请稍等..." -ForegroundColor Yellow
}

# 打开调试页面
Write-Host "🌐 打开调试页面..." -ForegroundColor Yellow
Start-Process "http://localhost:3000/debug-competitor-form.html"

Write-Host ""
Write-Host "📋 调试检查清单：" -ForegroundColor Cyan
Write-Host "□ 1. 打开浏览器开发者工具 (F12)" -ForegroundColor White
Write-Host "□ 2. 切换到 Console 标签页" -ForegroundColor White
Write-Host "□ 3. 访问竞品管理页面 (http://localhost:3000/competitors)" -ForegroundColor White
Write-Host "□ 4. 点击编辑按钮" -ForegroundColor White
Write-Host "□ 5. 修改竞品信息" -ForegroundColor White
Write-Host "□ 6. 点击'更新竞品'按钮" -ForegroundColor White
Write-Host "□ 7. 观察控制台输出的调试信息" -ForegroundColor White
Write-Host ""

Write-Host "🔍 预期的调试输出顺序：" -ForegroundColor Cyan
Write-Host "   🖱️ [CompetitorForm] 提交按钮被点击" -ForegroundColor Gray
Write-Host "   🚀 [CompetitorForm] 开始表单提交" -ForegroundColor Gray
Write-Host "   📝 [CompetitorForm] 清理后的数据" -ForegroundColor Gray
Write-Host "   📤 [CompetitorForm] 调用父组件onSubmit" -ForegroundColor Gray
Write-Host "   🚀 [CompetitorsPage] 开始处理表单提交" -ForegroundColor Gray
Write-Host "   📤 [CompetitorsPage] 发送API请求" -ForegroundColor Gray
Write-Host "   📥 [CompetitorsPage] 收到API响应" -ForegroundColor Gray
Write-Host "   ✅ [CompetitorsPage] 操作成功" -ForegroundColor Gray
Write-Host ""

Write-Host "⚠️  如果某个步骤的日志没有出现，说明问题出现在该步骤" -ForegroundColor Yellow
Write-Host ""

# 等待用户输入
Write-Host "按任意键停止服务器并退出..." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# 停止后台任务
Write-Host "🛑 停止开发服务器..." -ForegroundColor Yellow
Stop-Job $job -Force
Remove-Job $job -Force

Write-Host "✅ 调试测试完成！" -ForegroundColor Green
