'use client';

import React, { useState } from 'react';
import { CompetitorForm } from '@/components/forms/competitor-form';
import { Competitor, UpdateCompetitorInput } from '@/types';

// 模拟的竞品数据
const mockCompetitor: Competitor = {
  competitor_id: 1,
  competitor_name: '测试竞品名称',
  city: '深圳市',
  park_type: '主题公园',
  is_active: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};

export default function TestFormPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  const handleSubmit = async (data: UpdateCompetitorInput) => {
    console.log('🧪 [TestForm] 收到表单数据:', data);
    setLoading(true);
    setResult('');

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('✅ [TestForm] 模拟API调用成功');
      setResult('✅ 表单提交成功！数据: ' + JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('❌ [TestForm] 模拟API调用失败:', error);
      setResult('❌ 表单提交失败: ' + (error instanceof Error ? error.message : '未知错误'));
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">竞品表单测试页面</h1>
      
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">测试说明</h2>
        <p className="text-sm text-gray-600">
          这是一个独立的表单测试页面，用于验证竞品表单的功能。
          请打开浏览器开发者工具的Console标签页查看详细的调试信息。
        </p>
      </div>

      <CompetitorForm
        mode="edit"
        initialData={mockCompetitor}
        onSubmit={handleSubmit}
        loading={loading}
      />

      {result && (
        <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">测试结果</h3>
          <pre className="text-sm text-gray-700 whitespace-pre-wrap">{result}</pre>
        </div>
      )}

      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">调试步骤</h3>
        <ol className="text-sm text-gray-700 space-y-1">
          <li>1. 打开浏览器开发者工具 (F12)</li>
          <li>2. 切换到 Console 标签页</li>
          <li>3. 修改表单中的任意字段</li>
          <li>4. 点击"更新竞品"按钮</li>
          <li>5. 观察控制台输出的调试信息</li>
        </ol>
      </div>
    </div>
  );
}
