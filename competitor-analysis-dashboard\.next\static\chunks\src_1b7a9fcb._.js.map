{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/layout/mobile-nav.tsx"], "sourcesContent": ["/**\n * 移动端导航组件\n * \n * 功能说明：\n * 1. 专为移动端设计的导航菜单\n * 2. 底部标签栏导航\n * 3. 手势友好的交互设计\n * 4. 适配不同屏幕尺寸\n */\n\n'use client';\n\nimport React from 'react';\nimport { Home, Users, Ticket, Database, BarChart3, Settings, FileText } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\n// ============================================================================\n// 导航项配置\n// ============================================================================\n\ninterface NavItem {\n  key: string;\n  label: string;\n  icon: React.ComponentType<{ className?: string }>;\n  href: string;\n  badge?: number;\n}\n\nconst navItems: NavItem[] = [\n  {\n    key: 'dashboard',\n    label: '首页',\n    icon: Home,\n    href: '/'\n  },\n  {\n    key: 'competitors',\n    label: '竞品',\n    icon: Users,\n    href: '/competitors'\n  },\n  {\n    key: 'ticket-types',\n    label: '票种',\n    icon: Ticket,\n    href: '/ticket-types'\n  },\n  {\n    key: 'promotions',\n    label: '促销',\n    icon: Database,\n    href: '/promotions'\n  },\n  {\n    key: 'usage-rules',\n    label: '规则',\n    icon: FileText,\n    href: '/usage-rules'\n  },\n  {\n    key: 'analytics',\n    label: '分析',\n    icon: BarChart3,\n    href: '/analytics'\n  }\n];\n\n// ============================================================================\n// 移动端底部导航组件\n// ============================================================================\n\nexport interface MobileBottomNavProps {\n  currentPath?: string;\n  className?: string;\n}\n\nexport const MobileBottomNav: React.FC<MobileBottomNavProps> = ({\n  currentPath = '/',\n  className\n}) => {\n  return (\n    <nav className={cn(\n      'fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 md:hidden',\n      'safe-area-pb', // 适配iPhone底部安全区域\n      className\n    )}>\n      <div className=\"flex items-center justify-around px-2 py-1\">\n        {navItems.map((item) => {\n          const isActive = currentPath === item.href || \n            (item.href !== '/' && currentPath.startsWith(item.href));\n          const Icon = item.icon;\n\n          return (\n            <a\n              key={item.key}\n              href={item.href}\n              className={cn(\n                'flex flex-col items-center justify-center min-w-0 flex-1 px-1 py-2 text-xs transition-colors',\n                'active:bg-gray-100 rounded-lg',\n                isActive\n                  ? 'text-blue-600'\n                  : 'text-gray-600 hover:text-gray-900'\n              )}\n            >\n              <div className=\"relative\">\n                <Icon className={cn(\n                  'h-5 w-5 mb-1',\n                  isActive ? 'text-blue-600' : 'text-gray-400'\n                )} />\n                {item.badge && item.badge > 0 && (\n                  <span className=\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {item.badge > 99 ? '99+' : item.badge}\n                  </span>\n                )}\n              </div>\n              <span className={cn(\n                'truncate max-w-full',\n                isActive ? 'font-medium' : 'font-normal'\n              )}>\n                {item.label}\n              </span>\n            </a>\n          );\n        })}\n      </div>\n    </nav>\n  );\n};\n\n// ============================================================================\n// 移动端侧边抽屉导航组件\n// ============================================================================\n\nexport interface MobileDrawerNavProps {\n  open: boolean;\n  onClose: () => void;\n  currentPath?: string;\n  className?: string;\n}\n\nexport const MobileDrawerNav: React.FC<MobileDrawerNavProps> = ({\n  open,\n  onClose,\n  currentPath = '/',\n  className\n}) => {\n  return (\n    <>\n      {/* 遮罩层 */}\n      {open && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* 抽屉内容 */}\n      <div\n        className={cn(\n          'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:hidden',\n          open ? 'translate-x-0' : '-translate-x-full',\n          className\n        )}\n      >\n        {/* 抽屉头部 */}\n        <div className=\"flex items-center justify-between h-16 px-4 border-b border-gray-200\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">\n            竞品分析系统\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n          >\n            <span className=\"sr-only\">关闭菜单</span>\n            <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* 导航菜单 */}\n        <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n          {navItems.map((item) => {\n            const isActive = currentPath === item.href || \n              (item.href !== '/' && currentPath.startsWith(item.href));\n            const Icon = item.icon;\n\n            return (\n              <a\n                key={item.key}\n                href={item.href}\n                onClick={onClose}\n                className={cn(\n                  'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',\n                  isActive\n                    ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'\n                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                )}\n              >\n                <Icon className=\"mr-3 h-5 w-5\" />\n                <span className=\"flex-1\">{item.label}</span>\n                {item.badge && item.badge > 0 && (\n                  <span className=\"ml-2 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {item.badge > 99 ? '99+' : item.badge}\n                  </span>\n                )}\n              </a>\n            );\n          })}\n        </nav>\n\n        {/* 抽屉底部 */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <div className=\"text-xs text-gray-500 text-center\">\n            版本 1.0.0\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\n// ============================================================================\n// 移动端顶部导航栏组件\n// ============================================================================\n\nexport interface MobileTopNavProps {\n  title?: string;\n  onMenuClick?: () => void;\n  actions?: React.ReactNode;\n  className?: string;\n}\n\nexport const MobileTopNav: React.FC<MobileTopNavProps> = ({\n  title = '竞品分析系统',\n  onMenuClick,\n  actions,\n  className\n}) => {\n  return (\n    <header className={cn(\n      'sticky top-0 z-30 bg-white border-b border-gray-200 md:hidden',\n      className\n    )}>\n      <div className=\"flex items-center justify-between h-14 px-4\">\n        {/* 左侧菜单按钮 */}\n        <button\n          onClick={onMenuClick}\n          className=\"p-2 -ml-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n        >\n          <span className=\"sr-only\">打开菜单</span>\n          <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n          </svg>\n        </button>\n\n        {/* 中间标题 */}\n        <h1 className=\"text-lg font-semibold text-gray-900 truncate flex-1 mx-4 text-center\">\n          {title}\n        </h1>\n\n        {/* 右侧操作按钮 */}\n        <div className=\"flex items-center space-x-2\">\n          {actions}\n        </div>\n      </div>\n    </header>\n  );\n};\n\n// ============================================================================\n// 移动端浮动操作按钮组件\n// ============================================================================\n\nexport interface MobileFABProps {\n  onClick?: () => void;\n  icon?: React.ComponentType<{ className?: string }>;\n  label?: string;\n  className?: string;\n}\n\nexport const MobileFAB: React.FC<MobileFABProps> = ({\n  onClick,\n  icon: Icon = Database,\n  label = '添加',\n  className\n}) => {\n  return (\n    <button\n      onClick={onClick}\n      className={cn(\n        'fixed bottom-20 right-4 z-40 md:hidden',\n        'h-14 w-14 bg-blue-600 text-white rounded-full shadow-lg',\n        'flex items-center justify-center',\n        'hover:bg-blue-700 active:bg-blue-800',\n        'transition-colors duration-200',\n        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n        className\n      )}\n    >\n      <Icon className=\"h-6 w-6\" />\n      <span className=\"sr-only\">{label}</span>\n    </button>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  MobileBottomNav as default,\n  MobileDrawerNav,\n  MobileTopNav,\n  MobileFAB\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;AAKD;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAkBA,MAAM,WAAsB;IAC1B;QACE,KAAK;QACL,OAAO;QACP,MAAM,sMAAA,CAAA,OAAI;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;QACX,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,yMAAA,CAAA,SAAM;QACZ,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,6MAAA,CAAA,WAAQ;QACd,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,iNAAA,CAAA,WAAQ;QACd,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,qNAAA,CAAA,YAAS;QACf,MAAM;IACR;CACD;AAWM,MAAM,kBAAkD;QAAC,EAC9D,cAAc,GAAG,EACjB,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kFACA,gBACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;sBACZ,SAAS,GAAG,CAAC,CAAC;gBACb,MAAM,WAAW,gBAAgB,KAAK,IAAI,IACvC,KAAK,IAAI,KAAK,OAAO,YAAY,UAAU,CAAC,KAAK,IAAI;gBACxD,MAAM,OAAO,KAAK,IAAI;gBAEtB,qBACE,6LAAC;oBAEC,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,iCACA,WACI,kBACA;;sCAGN,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,gBACA,WAAW,kBAAkB;;;;;;gCAE9B,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,6LAAC;oCAAK,WAAU;8CACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;;;;;;sCAI3C,6LAAC;4BAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,uBACA,WAAW,gBAAgB;sCAE1B,KAAK,KAAK;;;;;;;mBAzBR,KAAK,GAAG;;;;;YA6BnB;;;;;;;;;;;AAIR;KAnDa;AAgEN,MAAM,kBAAkD;QAAC,EAC9D,IAAI,EACJ,OAAO,EACP,cAAc,GAAG,EACjB,SAAS,EACV;IACC,qBACE;;YAEG,sBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yHACA,OAAO,kBAAkB,qBACzB;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC9D,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,WAAW,gBAAgB,KAAK,IAAI,IACvC,KAAK,IAAI,KAAK,OAAO,YAAY,UAAU,CAAC,KAAK,IAAI;4BACxD,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,6LAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,SAAS;gCACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,yDACA;;kDAGN,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU,KAAK,KAAK;;;;;;oCACnC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,6LAAC;wCAAK,WAAU;kDACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;+BAdpC,KAAK,GAAG;;;;;wBAmBnB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;AAO7D;MAhFa;AA6FN,MAAM,eAA4C;QAAC,EACxD,QAAQ,QAAQ,EAChB,WAAW,EACX,OAAO,EACP,SAAS,EACV;IACC,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,iEACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,6LAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCAC9D,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;8BAKzE,6LAAC;oBAAG,WAAU;8BACX;;;;;;8BAIH,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;MAnCa;AAgDN,MAAM,YAAsC;QAAC,EAClD,OAAO,EACP,MAAM,OAAO,6MAAA,CAAA,WAAQ,EACrB,QAAQ,IAAI,EACZ,SAAS,EACV;IACC,qBACE,6LAAC;QACC,SAAS;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,2DACA,oCACA,wCACA,kCACA,2EACA;;0BAGF,6LAAC;gBAAK,WAAU;;;;;;0BAChB,6LAAC;gBAAK,WAAU;0BAAW;;;;;;;;;;;;AAGjC;MAvBa", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/layout/main-layout.tsx"], "sourcesContent": ["/**\n * 主布局组件\n * \n * 功能说明：\n * 1. 提供应用程序的主要布局结构\n * 2. 包含导航栏、侧边栏、主内容区域\n * 3. 响应式设计，支持移动端\n * 4. 可配置的布局选项\n */\n\nimport React, { useState } from 'react';\nimport { Menu, X, Home, Database, BarChart3, Settings, Users, Ticket, FileText } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { MobileBottomNav, MobileDrawerNav, MobileTopNav } from './mobile-nav';\n\n// ============================================================================\n// 导航菜单配置\n// ============================================================================\n\nexport interface MenuItem {\n  key: string;\n  label: string;\n  icon: React.ComponentType<{ className?: string }>;\n  href: string;\n  children?: MenuItem[];\n}\n\nconst defaultMenuItems: MenuItem[] = [\n  {\n    key: 'dashboard',\n    label: '仪表板',\n    icon: Home,\n    href: '/'\n  },\n  {\n    key: 'competitors',\n    label: '竞品管理',\n    icon: Users,\n    href: '/competitors'\n  },\n  {\n    key: 'ticket-types',\n    label: '票种管理',\n    icon: Ticket,\n    href: '/ticket-types'\n  },\n  {\n    key: 'promotions',\n    label: '促销活动',\n    icon: Database,\n    href: '/promotions'\n  },\n  {\n    key: 'usage-rules',\n    label: '使用规则',\n    icon: FileText,\n    href: '/usage-rules'\n  },\n  {\n    key: 'analytics',\n    label: '数据分析',\n    icon: BarChart3,\n    href: '/analytics'\n  },\n  {\n    key: 'settings',\n    label: '系统设置',\n    icon: Settings,\n    href: '/settings'\n  }\n];\n\n// ============================================================================\n// 布局组件接口\n// ============================================================================\n\nexport interface MainLayoutProps {\n  children: React.ReactNode;\n  menuItems?: MenuItem[];\n  currentPath?: string;\n  title?: string;\n  className?: string;\n}\n\n// ============================================================================\n// 主布局组件实现\n// ============================================================================\n\nexport const MainLayout: React.FC<MainLayoutProps> = ({\n  children,\n  menuItems = defaultMenuItems,\n  currentPath = '/',\n  title = '竞品分析管理系统',\n  className\n}) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // 渲染菜单项\n  const renderMenuItem = (item: MenuItem) => {\n    const isActive = currentPath === item.href || currentPath.startsWith(item.href + '/');\n    const Icon = item.icon;\n\n    return (\n      <a\n        key={item.key}\n        href={item.href}\n        className={cn(\n          'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',\n          isActive\n            ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'\n            : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n        )}\n        onClick={() => setSidebarOpen(false)}\n      >\n        <Icon className=\"mr-3 h-5 w-5\" />\n        {item.label}\n      </a>\n    );\n  };\n\n  return (\n    <div className={cn('min-h-screen bg-gray-50', className)}>\n      {/* 移动端导航组件 */}\n      <MobileDrawerNav\n        open={sidebarOpen}\n        onClose={() => setSidebarOpen(false)}\n        currentPath={currentPath}\n      />\n\n      {/* 桌面端侧边栏 */}\n      <div\n        className={cn(\n          'hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-50 lg:w-64 lg:bg-white lg:shadow-lg lg:block'\n        )}\n      >\n        {/* 侧边栏头部 */}\n        <div className=\"flex items-center justify-between h-16 px-4 border-b border-gray-200\">\n          <h1 className=\"text-lg font-semibold text-gray-900 truncate\">\n            {title}\n          </h1>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          >\n            <X className=\"h-5 w-5\" />\n          </Button>\n        </div>\n\n        {/* 导航菜单 */}\n        <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n          {menuItems.map(renderMenuItem)}\n        </nav>\n\n        {/* 侧边栏底部 */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <div className=\"text-xs text-gray-500 text-center\">\n            版本 1.0.0\n          </div>\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div className=\"lg:pl-64\">\n        {/* 移动端顶部导航 */}\n        <MobileTopNav\n          title={menuItems.find(item => item.href === currentPath)?.label || '首页'}\n          onMenuClick={() => setSidebarOpen(true)}\n        />\n\n        {/* 桌面端顶部导航栏 */}\n        <header className=\"hidden lg:block bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\n\n            {/* 页面标题 */}\n            <div className=\"flex-1 lg:flex-none\">\n              <h2 className=\"text-xl font-semibold text-gray-900\">\n                {/* 这里可以根据当前路径动态显示页面标题 */}\n                {menuItems.find(item => item.href === currentPath)?.label || '首页'}\n              </h2>\n            </div>\n\n            {/* 用户操作区域 */}\n            <div className=\"flex items-center space-x-4\">\n              {/* 通知按钮 */}\n              <Button variant=\"ghost\" size=\"sm\">\n                <span className=\"sr-only\">通知</span>\n                <div className=\"h-5 w-5 rounded-full bg-gray-300\" />\n              </Button>\n\n              {/* 用户头像 */}\n              <Button variant=\"ghost\" size=\"sm\">\n                <span className=\"sr-only\">用户菜单</span>\n                <div className=\"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium\">\n                  U\n                </div>\n              </Button>\n            </div>\n          </div>\n        </header>\n\n        {/* 主内容 */}\n        <main className=\"flex-1 pb-16 lg:pb-0\">\n          <div className=\"p-4 sm:p-6 lg:p-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n\n      {/* 移动端底部导航 */}\n      <MobileBottomNav currentPath={currentPath} />\n    </div>\n  );\n};\n\n// ============================================================================\n// 页面容器组件\n// ============================================================================\n\nexport interface PageContainerProps {\n  title?: string;\n  description?: string;\n  actions?: React.ReactNode;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const PageContainer: React.FC<PageContainerProps> = ({\n  title,\n  description,\n  actions,\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('space-y-6', className)}>\n      {/* 页面头部 */}\n      {(title || description || actions) && (\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div>\n            {title && (\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                {title}\n              </h1>\n            )}\n            {description && (\n              <p className=\"mt-1 text-sm text-gray-600\">\n                {description}\n              </p>\n            )}\n          </div>\n          {actions && (\n            <div className=\"flex gap-2\">\n              {actions}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 页面内容 */}\n      <div>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 卡片容器组件\n// ============================================================================\n\nexport interface CardContainerProps {\n  title?: string;\n  description?: string;\n  actions?: React.ReactNode;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardContainer: React.FC<CardContainerProps> = ({\n  title,\n  description,\n  actions,\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('bg-white rounded-lg shadow border border-gray-200', className)}>\n      {/* 卡片头部 */}\n      {(title || description || actions) && (\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              {title && (\n                <h3 className=\"text-lg font-medium text-gray-900\">\n                  {title}\n                </h3>\n              )}\n              {description && (\n                <p className=\"mt-1 text-sm text-gray-600\">\n                  {description}\n                </p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex gap-2\">\n                {actions}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* 卡片内容 */}\n      <div className=\"p-6\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  MainLayout as default,\n  PageContainer,\n  CardContainer\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;AAED;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;AAcA,MAAM,mBAA+B;IACnC;QACE,KAAK;QACL,OAAO;QACP,MAAM,sMAAA,CAAA,OAAI;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;QACX,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,yMAAA,CAAA,SAAM;QACZ,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,6MAAA,CAAA,WAAQ;QACd,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,iNAAA,CAAA,WAAQ;QACd,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,qNAAA,CAAA,YAAS;QACf,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,6MAAA,CAAA,WAAQ;QACd,MAAM;IACR;CACD;AAkBM,MAAM,aAAwC;QAAC,EACpD,QAAQ,EACR,YAAY,gBAAgB,EAC5B,cAAc,GAAG,EACjB,QAAQ,UAAU,EAClB,SAAS,EACV;QAyEgB,iBAYA;;IApFf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,QAAQ;IACR,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW,gBAAgB,KAAK,IAAI,IAAI,YAAY,UAAU,CAAC,KAAK,IAAI,GAAG;QACjF,MAAM,OAAO,KAAK,IAAI;QAEtB,qBACE,6LAAC;YAEC,MAAM,KAAK,IAAI;YACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,yDACA;YAEN,SAAS,IAAM,eAAe;;8BAE9B,6LAAC;oBAAK,WAAU;;;;;;gBACf,KAAK,KAAK;;WAXN,KAAK,GAAG;;;;;IAcnB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAE5C,6LAAC,gJAAA,CAAA,kBAAe;gBACd,MAAM;gBACN,SAAS,IAAM,eAAe;gBAC9B,aAAa;;;;;;0BAIf,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX;;;;;;0CAEH,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAoC;;;;;;;;;;;;;;;;;0BAOvD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,gJAAA,CAAA,eAAY;wBACX,OAAO,EAAA,kBAAA,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,0BAArC,sCAAA,gBAAmD,KAAK,KAAI;wBACnE,aAAa,IAAM,eAAe;;;;;;kCAIpC,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CAGb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDAEX,EAAA,mBAAA,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,0BAArC,uCAAA,iBAAmD,KAAK,KAAI;;;;;;;;;;;8CAKjE,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAIjB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;8DAAmG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS1H,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;0BAMP,6LAAC,gJAAA,CAAA,kBAAe;gBAAC,aAAa;;;;;;;;;;;;AAGpC;GA9Ha;KAAA;AA4IN,MAAM,gBAA8C;QAAC,EAC1D,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;YAE7B,CAAC,SAAS,eAAe,OAAO,mBAC/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BACE,uBACC,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAGJ,6BACC,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;oBAIN,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;0BAOT,6LAAC;0BACE;;;;;;;;;;;;AAIT;MAtCa;AAoDN,MAAM,gBAA8C;QAAC,EAC1D,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;;YAErE,CAAC,SAAS,eAAe,OAAO,mBAC/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;gCACE,uBACC,6LAAC;oCAAG,WAAU;8CACX;;;;;;gCAGJ,6BACC,6LAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;wBAIN,yBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;0BAQX,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;MAxCa", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/loading.tsx"], "sourcesContent": ["/**\n * 加载组件\n * \n * 功能说明：\n * 1. 提供多种加载状态的显示\n * 2. 支持不同尺寸和样式\n * 3. 可配置加载文本和动画\n */\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\n// ============================================================================\n// 加载动画组件\n// ============================================================================\n\n/**\n * 旋转加载器组件\n */\nexport interface SpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport const Spinner: React.FC<SpinnerProps> = ({ \n  size = 'md', \n  className \n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        sizeClasses[size],\n        className\n      )}\n    />\n  );\n};\n\n/**\n * 脉冲加载器组件\n */\nexport interface PulseProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport const Pulse: React.FC<PulseProps> = ({ \n  size = 'md', \n  className \n}) => {\n  const sizeClasses = {\n    sm: 'w-2 h-2',\n    md: 'w-3 h-3',\n    lg: 'w-4 h-4'\n  };\n\n  return (\n    <div className={cn('flex space-x-1', className)}>\n      {[0, 1, 2].map((i) => (\n        <div\n          key={i}\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n          style={{\n            animationDelay: `${i * 0.2}s`,\n            animationDuration: '1s'\n          }}\n        />\n      ))}\n    </div>\n  );\n};\n\n// ============================================================================\n// 加载状态组件\n// ============================================================================\n\n/**\n * 内联加载组件\n */\nexport interface LoadingInlineProps {\n  text?: string;\n  size?: 'sm' | 'md' | 'lg';\n  type?: 'spinner' | 'pulse';\n  className?: string;\n}\n\nexport const LoadingInline: React.FC<LoadingInlineProps> = ({\n  text = '加载中...',\n  size = 'md',\n  type = 'spinner',\n  className\n}) => {\n  const LoaderComponent = type === 'spinner' ? Spinner : Pulse;\n\n  return (\n    <div className={cn('flex items-center space-x-2', className)}>\n      <LoaderComponent size={size} />\n      {text && (\n        <span className=\"text-sm text-gray-600 animate-pulse\">\n          {text}\n        </span>\n      )}\n    </div>\n  );\n};\n\n/**\n * 页面加载组件\n */\nexport interface LoadingPageProps {\n  text?: string;\n  description?: string;\n  className?: string;\n}\n\nexport const LoadingPage: React.FC<LoadingPageProps> = ({\n  text = '加载中...',\n  description,\n  className\n}) => {\n  return (\n    <div className={cn(\n      'flex flex-col items-center justify-center min-h-[400px] space-y-4',\n      className\n    )}>\n      <Spinner size=\"lg\" />\n      <div className=\"text-center space-y-2\">\n        <h3 className=\"text-lg font-medium text-gray-900\">\n          {text}\n        </h3>\n        {description && (\n          <p className=\"text-sm text-gray-500 max-w-sm\">\n            {description}\n          </p>\n        )}\n      </div>\n    </div>\n  );\n};\n\n/**\n * 覆盖层加载组件\n */\nexport interface LoadingOverlayProps {\n  visible: boolean;\n  text?: string;\n  className?: string;\n  children?: React.ReactNode;\n}\n\nexport const LoadingOverlay: React.FC<LoadingOverlayProps> = ({\n  visible,\n  text = '处理中...',\n  className,\n  children\n}) => {\n  if (!visible) {\n    return <>{children}</>;\n  }\n\n  return (\n    <div className={cn('relative', className)}>\n      {children}\n      <div className=\"absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50\">\n        <div className=\"bg-white rounded-lg shadow-lg p-6 flex flex-col items-center space-y-4\">\n          <Spinner size=\"lg\" />\n          <p className=\"text-sm font-medium text-gray-900\">\n            {text}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n/**\n * 表格行加载组件\n */\nexport interface LoadingTableRowProps {\n  columns: number;\n  rows?: number;\n  className?: string;\n}\n\nexport const LoadingTableRow: React.FC<LoadingTableRowProps> = ({\n  columns,\n  rows = 5,\n  className\n}) => {\n  return (\n    <>\n      {Array.from({ length: rows }).map((_, rowIndex) => (\n        <tr key={rowIndex} className={className}>\n          {Array.from({ length: columns }).map((_, colIndex) => (\n            <td key={colIndex} className=\"px-4 py-3\">\n              <div className=\"h-4 bg-gray-200 rounded animate-pulse\" />\n            </td>\n          ))}\n        </tr>\n      ))}\n    </>\n  );\n};\n\n/**\n * 卡片加载组件\n */\nexport interface LoadingCardProps {\n  className?: string;\n}\n\nexport const LoadingCard: React.FC<LoadingCardProps> = ({ className }) => {\n  return (\n    <div className={cn(\n      'border rounded-lg p-6 space-y-4 animate-pulse',\n      className\n    )}>\n      <div className=\"h-4 bg-gray-200 rounded w-3/4\" />\n      <div className=\"space-y-2\">\n        <div className=\"h-3 bg-gray-200 rounded\" />\n        <div className=\"h-3 bg-gray-200 rounded w-5/6\" />\n      </div>\n      <div className=\"flex space-x-2\">\n        <div className=\"h-6 bg-gray-200 rounded w-16\" />\n        <div className=\"h-6 bg-gray-200 rounded w-20\" />\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 按钮加载状态组件\n// ============================================================================\n\n/**\n * 带加载状态的按钮组件\n */\nexport interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  loading?: boolean;\n  loadingText?: string;\n  children: React.ReactNode;\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n}\n\nexport const LoadingButton: React.FC<LoadingButtonProps> = ({\n  loading = false,\n  loadingText,\n  children,\n  disabled,\n  className,\n  onClick,\n  ...props\n}) => {\n  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {\n    console.log('🖱️ [LoadingButton] 按钮点击事件', { loading, disabled, type: props.type });\n    if (onClick) {\n      onClick(e);\n    }\n  };\n\n  return (\n    <button\n      {...props}\n      onClick={handleClick}\n      disabled={disabled || loading}\n      className={cn(\n        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',\n        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n        'disabled:pointer-events-none disabled:opacity-50',\n        'bg-primary text-primary-foreground hover:bg-primary/90',\n        'h-10 px-4 py-2',\n        className\n      )}\n    >\n      {loading && (\n        <Spinner size=\"sm\" className=\"mr-2\" />\n      )}\n      {loading && loadingText ? loadingText : children}\n    </button>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  Spinner,\n  Pulse,\n  LoadingInline,\n  LoadingPage,\n  LoadingOverlay,\n  LoadingTableRow,\n  LoadingCard,\n  LoadingButton\n};\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;AAGD;;;AAcO,MAAM,UAAkC;QAAC,EAC9C,OAAO,IAAI,EACX,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;KAnBa;AA6BN,MAAM,QAA8B;QAAC,EAC1C,OAAO,IAAI,EACX,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;gBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;gBAEnB,OAAO;oBACL,gBAAgB,AAAC,GAAU,OAAR,IAAI,KAAI;oBAC3B,mBAAmB;gBACrB;eARK;;;;;;;;;;AAaf;MA3Ba;AA2CN,MAAM,gBAA8C;QAAC,EAC1D,OAAO,QAAQ,EACf,OAAO,IAAI,EACX,OAAO,SAAS,EAChB,SAAS,EACV;IACC,MAAM,kBAAkB,SAAS,YAAY,UAAU;IAEvD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;0BAChD,6LAAC;gBAAgB,MAAM;;;;;;YACtB,sBACC,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAKX;MAlBa;AA6BN,MAAM,cAA0C;QAAC,EACtD,OAAO,QAAQ,EACf,WAAW,EACX,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qEACA;;0BAEA,6LAAC;gBAAQ,MAAK;;;;;;0BACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX;;;;;;oBAEF,6BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;MAvBa;AAmCN,MAAM,iBAAgD;QAAC,EAC5D,OAAO,EACP,OAAO,QAAQ,EACf,SAAS,EACT,QAAQ,EACT;IACC,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;YAC5B;0BACD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAQ,MAAK;;;;;;sCACd,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAMb;MAvBa;AAkCN,MAAM,kBAAkD;QAAC,EAC9D,OAAO,EACP,OAAO,CAAC,EACR,SAAS,EACV;IACC,qBACE;kBACG,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAK,GAAG,GAAG,CAAC,CAAC,GAAG,yBACpC,6LAAC;gBAAkB,WAAW;0BAC3B,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACvC,6LAAC;wBAAkB,WAAU;kCAC3B,cAAA,6LAAC;4BAAI,WAAU;;;;;;uBADR;;;;;eAFJ;;;;;;AAUjB;MAlBa;AA2BN,MAAM,cAA0C;QAAC,EAAE,SAAS,EAAE;IACnE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,iDACA;;0BAEA,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;MAjBa;AAkCN,MAAM,gBAA8C;QAAC,EAC1D,UAAU,KAAK,EACf,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,OAAO,EACP,GAAG,OACJ;IACC,MAAM,cAAc,CAAC;QACnB,QAAQ,GAAG,CAAC,8BAA8B;YAAE;YAAS;YAAU,MAAM,MAAM,IAAI;QAAC;QAChF,IAAI,SAAS;YACX,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC;QACE,GAAG,KAAK;QACT,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA,uGACA,oDACA,0DACA,kBACA;;YAGD,yBACC,6LAAC;gBAAQ,MAAK;gBAAK,WAAU;;;;;;YAE9B,WAAW,cAAc,cAAc;;;;;;;AAG9C;MApCa", "debugId": null}}, {"offset": {"line": 1291, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1344, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1466, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/app/page.tsx"], "sourcesContent": ["/**\n * 首页仪表板页面\n * \n * 功能说明：\n * 1. 显示系统整体统计信息\n * 2. 展示关键数据指标\n * 3. 提供快速导航入口\n * 4. 响应式设计\n */\n\n'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { Users, Ticket, Database, TrendingUp } from 'lucide-react';\nimport { MainLayout, PageContainer, CardContainer } from '@/components/layout/main-layout';\nimport { LoadingPage } from '@/components/ui/loading';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\n\n// ============================================================================\n// 统计数据接口定义\n// ============================================================================\n\ninterface DashboardStats {\n  competitors: {\n    total: number;\n    active: number;\n    inactive: number;\n    cityCount: number;\n    parkTypeCount: number;\n  };\n  ticketTypes: {\n    total: number;\n    categoryCount: number;\n    withPromotions: number;\n    withoutPromotions: number;\n  };\n  promotions: {\n    total: number;\n    activePromotions: number;\n    expiredPromotions: number;\n    avgDiscountRate: number;\n    avgPrice: number;\n    totalCompetitors: number;\n    totalTicketTypes: number;\n  };\n  summary: {\n    totalRecords: number;\n    lastUpdated: string;\n  };\n}\n\n// ============================================================================\n// 统计卡片组件\n// ============================================================================\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  description?: string;\n  icon: React.ComponentType<{ className?: string }>;\n  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple';\n}\n\nconst StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  description,\n  icon: Icon,\n  color = 'blue'\n}) => {\n  const colorClasses = {\n    blue: 'bg-blue-50 text-blue-600',\n    green: 'bg-green-50 text-green-600',\n    yellow: 'bg-yellow-50 text-yellow-600',\n    red: 'bg-red-50 text-red-600',\n    purple: 'bg-purple-50 text-purple-600'\n  };\n\n  return (\n    <Card>\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n        <CardTitle className=\"text-sm font-medium text-gray-600\">\n          {title}\n        </CardTitle>\n        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>\n          <Icon className=\"h-4 w-4\" />\n        </div>\n      </CardHeader>\n      <CardContent>\n        <div className=\"text-2xl font-bold text-gray-900\">\n          {typeof value === 'number' ? value.toLocaleString() : value}\n        </div>\n        {description && (\n          <p className=\"text-xs text-gray-500 mt-1\">\n            {description}\n          </p>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n\n// ============================================================================\n// 首页组件实现\n// ============================================================================\n\nexport default function HomePage() {\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStats = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('/api/statistics');\n        \n        if (!response.ok) {\n          throw new Error('获取统计数据失败');\n        }\n\n        const result = await response.json();\n        if (result.success) {\n          setStats(result.data);\n        } else {\n          throw new Error(result.error?.message || '获取统计数据失败');\n        }\n      } catch (err) {\n        setError(err instanceof Error ? err.message : '未知错误');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, []);\n\n  // 加载状态\n  if (loading) {\n    return (\n      <MainLayout>\n        <LoadingPage text=\"加载仪表板数据...\" />\n      </MainLayout>\n    );\n  }\n\n  // 错误状态\n  if (error || !stats) {\n    return (\n      <MainLayout>\n        <PageContainer>\n          <div className=\"text-center py-12\">\n            <div className=\"text-red-500 text-lg font-medium mb-4\">\n              {error || '数据加载失败'}\n            </div>\n            <Button onClick={() => window.location.reload()}>\n              重新加载\n            </Button>\n          </div>\n        </PageContainer>\n      </MainLayout>\n    );\n  }\n\n  return (\n    <MainLayout>\n      <PageContainer\n        title=\"仪表板\"\n        description=\"竞品分析系统数据概览\"\n      >\n        <div className=\"space-y-6\">\n          {/* 核心指标卡片 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <StatCard\n              title=\"竞品总数\"\n              value={stats.competitors.total}\n              description={`活跃: ${stats.competitors.active} | 非活跃: ${stats.competitors.inactive}`}\n              icon={Users}\n              color=\"blue\"\n            />\n            \n            <StatCard\n              title=\"票种总数\"\n              value={stats.ticketTypes.total}\n              description={`分类数: ${stats.ticketTypes.categoryCount}`}\n              icon={Ticket}\n              color=\"green\"\n            />\n            \n            <StatCard\n              title=\"促销活动\"\n              value={stats.promotions.total}\n              description={`活跃: ${stats.promotions.activePromotions} | 已过期: ${stats.promotions.expiredPromotions}`}\n              icon={Database}\n              color=\"purple\"\n            />\n            \n            <StatCard\n              title=\"平均折扣率\"\n              value={`${(stats.promotions.avgDiscountRate * 100).toFixed(1)}%`}\n              description={`平均价格: ¥${stats.promotions.avgPrice.toFixed(2)}`}\n              icon={TrendingUp}\n              color=\"yellow\"\n            />\n          </div>\n\n          {/* 详细统计信息 */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* 竞品分析 */}\n            <CardContainer\n              title=\"竞品分析\"\n              description=\"竞品数据分布情况\"\n            >\n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">总竞品数</span>\n                  <span className=\"font-medium\">{stats.competitors.total}</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">活跃竞品</span>\n                  <Badge variant=\"default\">{stats.competitors.active}</Badge>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">非活跃竞品</span>\n                  <Badge variant=\"secondary\">{stats.competitors.inactive}</Badge>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">覆盖城市数</span>\n                  <span className=\"font-medium\">{stats.competitors.cityCount}</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">园区类型数</span>\n                  <span className=\"font-medium\">{stats.competitors.parkTypeCount}</span>\n                </div>\n              </div>\n            </CardContainer>\n\n            {/* 票种分析 */}\n            <CardContainer\n              title=\"票种分析\"\n              description=\"票种数据分布情况\"\n            >\n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">总票种数</span>\n                  <span className=\"font-medium\">{stats.ticketTypes.total}</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">票种分类数</span>\n                  <span className=\"font-medium\">{stats.ticketTypes.categoryCount}</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">有促销活动</span>\n                  <Badge variant=\"default\">{stats.ticketTypes.withPromotions}</Badge>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">无促销活动</span>\n                  <Badge variant=\"secondary\">{stats.ticketTypes.withoutPromotions}</Badge>\n                </div>\n              </div>\n            </CardContainer>\n          </div>\n\n          {/* 快速操作 */}\n          <CardContainer\n            title=\"快速操作\"\n            description=\"常用功能快速入口\"\n          >\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <Button \n                variant=\"outline\" \n                className=\"h-20 flex flex-col items-center justify-center space-y-2\"\n                onClick={() => window.location.href = '/competitors'}\n              >\n                <Users className=\"h-6 w-6\" />\n                <span>管理竞品</span>\n              </Button>\n              \n              <Button \n                variant=\"outline\" \n                className=\"h-20 flex flex-col items-center justify-center space-y-2\"\n                onClick={() => window.location.href = '/ticket-types'}\n              >\n                <Ticket className=\"h-6 w-6\" />\n                <span>管理票种</span>\n              </Button>\n              \n              <Button \n                variant=\"outline\" \n                className=\"h-20 flex flex-col items-center justify-center space-y-2\"\n                onClick={() => window.location.href = '/promotions'}\n              >\n                <Database className=\"h-6 w-6\" />\n                <span>管理促销活动</span>\n              </Button>\n            </div>\n          </CardContainer>\n\n          {/* 系统信息 */}\n          <div className=\"text-center text-sm text-gray-500\">\n            <p>\n              总记录数: {stats.summary.totalRecords.toLocaleString()} | \n              最后更新: {new Date(stats.summary.lastUpdated).toLocaleString()}\n            </p>\n          </div>\n        </div>\n      </PageContainer>\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AAID;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAuDA,MAAM,WAAoC;QAAC,EACzC,KAAK,EACL,KAAK,EACL,WAAW,EACX,MAAM,IAAI,EACV,QAAQ,MAAM,EACf;IACC,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,KAAK;QACL,QAAQ;IACV;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;kCAClB;;;;;;kCAEH,6LAAC;wBAAI,WAAW,AAAC,kBAAqC,OAApB,YAAY,CAAC,MAAM;kCACnD,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;;0BAGpB,6LAAC,mIAAA,CAAA,cAAW;;kCACV,6LAAC;wBAAI,WAAU;kCACZ,OAAO,UAAU,WAAW,MAAM,cAAc,KAAK;;;;;;oBAEvD,6BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;KArCM;AA2CS,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;iDAAa;oBACjB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,MAAM;wBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;wBAClC,IAAI,OAAO,OAAO,EAAE;4BAClB,SAAS,OAAO,IAAI;wBACtB,OAAO;gCACW;4BAAhB,MAAM,IAAI,MAAM,EAAA,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,OAAO,KAAI;wBAC3C;oBACF,EAAE,OAAO,KAAK;wBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAChD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,OAAO;IACP,IAAI,SAAS;QACX,qBACE,6LAAC,iJAAA,CAAA,aAAU;sBACT,cAAA,6LAAC,sIAAA,CAAA,cAAW;gBAAC,MAAK;;;;;;;;;;;IAGxB;IAEA,OAAO;IACP,IAAI,SAAS,CAAC,OAAO;QACnB,qBACE,6LAAC,iJAAA,CAAA,aAAU;sBACT,cAAA,6LAAC,iJAAA,CAAA,gBAAa;0BACZ,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,SAAS;;;;;;sCAEZ,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;sCAAI;;;;;;;;;;;;;;;;;;;;;;IAO3D;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC,iJAAA,CAAA,gBAAa;YACZ,OAAM;YACN,aAAY;sBAEZ,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAM;gCACN,OAAO,MAAM,WAAW,CAAC,KAAK;gCAC9B,aAAa,AAAC,OAAyC,OAAnC,MAAM,WAAW,CAAC,MAAM,EAAC,YAAqC,OAA3B,MAAM,WAAW,CAAC,QAAQ;gCACjF,MAAM,uMAAA,CAAA,QAAK;gCACX,OAAM;;;;;;0CAGR,6LAAC;gCACC,OAAM;gCACN,OAAO,MAAM,WAAW,CAAC,KAAK;gCAC9B,aAAa,AAAC,QAAuC,OAAhC,MAAM,WAAW,CAAC,aAAa;gCACpD,MAAM,yMAAA,CAAA,SAAM;gCACZ,OAAM;;;;;;0CAGR,6LAAC;gCACC,OAAM;gCACN,OAAO,MAAM,UAAU,CAAC,KAAK;gCAC7B,aAAa,AAAC,OAAkD,OAA5C,MAAM,UAAU,CAAC,gBAAgB,EAAC,YAA6C,OAAnC,MAAM,UAAU,CAAC,iBAAiB;gCAClG,MAAM,6MAAA,CAAA,WAAQ;gCACd,OAAM;;;;;;0CAGR,6LAAC;gCACC,OAAM;gCACN,OAAO,AAAC,GAAsD,OAApD,CAAC,MAAM,UAAU,CAAC,eAAe,GAAG,GAAG,EAAE,OAAO,CAAC,IAAG;gCAC9D,aAAa,AAAC,UAA8C,OAArC,MAAM,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;gCACzD,MAAM,qNAAA,CAAA,aAAU;gCAChB,OAAM;;;;;;;;;;;;kCAKV,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,iJAAA,CAAA,gBAAa;gCACZ,OAAM;gCACN,aAAY;0CAEZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAe,MAAM,WAAW,CAAC,KAAK;;;;;;;;;;;;sDAExD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW,MAAM,WAAW,CAAC,MAAM;;;;;;;;;;;;sDAEpD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAa,MAAM,WAAW,CAAC,QAAQ;;;;;;;;;;;;sDAExD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAe,MAAM,WAAW,CAAC,SAAS;;;;;;;;;;;;sDAE5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAe,MAAM,WAAW,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;0CAMpE,6LAAC,iJAAA,CAAA,gBAAa;gCACZ,OAAM;gCACN,aAAY;0CAEZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAe,MAAM,WAAW,CAAC,KAAK;;;;;;;;;;;;sDAExD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAe,MAAM,WAAW,CAAC,aAAa;;;;;;;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW,MAAM,WAAW,CAAC,cAAc;;;;;;;;;;;;sDAE5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAa,MAAM,WAAW,CAAC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOvE,6LAAC,iJAAA,CAAA,gBAAa;wBACZ,OAAM;wBACN,aAAY;kCAEZ,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;sDAEtC,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;sDAEtC,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;sDAEtC,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;gCAAE;gCACM,MAAM,OAAO,CAAC,YAAY,CAAC,cAAc;gCAAG;gCAC5C,IAAI,KAAK,MAAM,OAAO,CAAC,WAAW,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvE;GA5MwB;MAAA", "debugId": null}}]}