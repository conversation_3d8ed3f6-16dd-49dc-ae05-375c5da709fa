/**
 * 促销活动Excel模板下载API
 * 
 * 功能说明：
 * 1. 生成标准的Excel模板文件
 * 2. 包含所有必需的列标题和数据格式说明
 * 3. 提供示例数据帮助用户理解格式
 * 4. 支持数据验证规则和格式说明
 */

import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import { ExcelTemplateColumn } from '@/types';

// ============================================================================
// Excel模板列定义
// ============================================================================

/**
 * 促销活动Excel模板列配置
 */
const TEMPLATE_COLUMNS: ExcelTemplateColumn[] = [
  {
    field: 'competitor_name',
    header: '竞品名称',
    required: true,
    type: 'string',
    example: '上海迪士尼乐园',
    description: '必填，竞品的完整名称'
  },
  {
    field: 'ticket_type_name',
    header: '票种名称',
    required: true,
    type: 'string',
    example: '成人票',
    description: '必填，票种的完整名称'
  },
  {
    field: 'activity_name',
    header: '活动名称',
    required: true,
    type: 'string',
    example: '春节特惠活动',
    description: '必填，促销活动的名称'
  },
  {
    field: 'rack_rate',
    header: '门市价',
    required: false,
    type: 'number',
    example: '399',
    description: '可选，原价格，数字格式'
  },
  {
    field: 'promo_price',
    header: '促销价',
    required: false,
    type: 'number',
    example: '299',
    description: '可选，促销价格，数字格式'
  },
  {
    field: 'sale_start_date',
    header: '销售开始日期',
    required: false,
    type: 'date',
    example: '2024-01-01',
    description: '可选，格式：YYYY-MM-DD'
  },
  {
    field: 'sale_end_date',
    header: '销售结束日期',
    required: false,
    type: 'date',
    example: '2024-12-31',
    description: '可选，格式：YYYY-MM-DD'
  },
  {
    field: 'use_start_date',
    header: '使用开始日期',
    required: false,
    type: 'date',
    example: '2024-01-01',
    description: '可选，格式：YYYY-MM-DD'
  },
  {
    field: 'use_end_date',
    header: '使用结束日期',
    required: false,
    type: 'date',
    example: '2024-12-31',
    description: '可选，格式：YYYY-MM-DD'
  },
  {
    field: 'sales_channel',
    header: '销售渠道',
    required: false,
    type: 'string',
    example: '官网,携程,美团',
    description: '可选，多个渠道用逗号分隔'
  },
  {
    field: 'usage_rules',
    header: '使用规则',
    required: false,
    type: 'string',
    example: '仅限平日使用',
    description: '可选，使用限制和规则说明（如：无限制、仅限平日使用、周末及法定节假日不可用等）'
  },
  {
    field: 'data_source_url',
    header: '数据来源URL',
    required: false,
    type: 'string',
    example: 'https://example.com/promotion',
    description: '可选，数据来源的网址'
  },
  {
    field: 'remarks',
    header: '备注',
    required: false,
    type: 'string',
    example: '春节期间特别优惠',
    description: '可选，其他备注信息'
  }
];

// ============================================================================
// API处理函数
// ============================================================================

/**
 * GET请求处理 - 下载Excel模板
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📥 开始生成促销活动Excel模板');

    // 创建工作簿
    const workbook = XLSX.utils.book_new();

    // 准备数据表格
    const worksheetData = [];

    // 第一行：列标题
    const headers = TEMPLATE_COLUMNS.map(col => col.header);
    worksheetData.push(headers);

    // 第二行：字段说明
    const descriptions = TEMPLATE_COLUMNS.map(col => 
      col.required ? `${col.description}（必填）` : col.description || ''
    );
    worksheetData.push(descriptions);

    // 第三行：示例数据
    const examples = TEMPLATE_COLUMNS.map(col => col.example || '');
    worksheetData.push(examples);

    // 第四行：空行，供用户填写
    const emptyRow = TEMPLATE_COLUMNS.map(() => '');
    worksheetData.push(emptyRow);

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // 设置列宽
    const columnWidths = TEMPLATE_COLUMNS.map(col => {
      switch (col.field) {
        case 'activity_name':
        case 'usage_rules':
        case 'data_source_url':
          return { wch: 30 };
        case 'competitor_name':
        case 'ticket_type_name':
          return { wch: 20 };
        case 'sales_channel':
        case 'remarks':
          return { wch: 25 };
        default:
          return { wch: 15 };
      }
    });
    worksheet['!cols'] = columnWidths;

    // 设置行高
    worksheet['!rows'] = [
      { hpt: 20 }, // 标题行
      { hpt: 40 }, // 说明行
      { hpt: 20 }, // 示例行
      { hpt: 20 }  // 空行
    ];

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '促销活动导入模板');

    // 创建说明工作表
    const instructionData = [
      ['促销活动批量导入说明'],
      [''],
      ['1. 填写要求：'],
      ['   - 竞品名称和票种名称必须填写，且必须是系统中已存在的'],
      ['   - 活动名称必须填写'],
      ['   - 价格字段请填写数字，不要包含货币符号'],
      ['   - 日期格式必须为：YYYY-MM-DD（如：2024-01-01）'],
      ['   - 销售渠道可以填写多个，用逗号分隔'],
      [''],
      ['2. 数据验证：'],
      ['   - 系统会自动验证竞品名称和票种名称是否存在'],
      ['   - 会检查日期格式和数值格式的正确性'],
      [''],
      ['3. 导入流程：'],
      ['   - 下载此模板文件'],
      ['   - 在"促销活动导入模板"工作表中填写数据'],
      ['   - 删除示例数据行和说明行，只保留标题行和数据行'],
      ['   - 保存文件并上传导入'],
      [''],
      ['4. 注意事项：'],
      ['   - 请不要修改列标题'],
      ['   - 建议一次导入不超过1000条记录'],
      ['   - 导入过程中如有错误，系统会提供详细的错误报告']
    ];

    const instructionSheet = XLSX.utils.aoa_to_sheet(instructionData);
    instructionSheet['!cols'] = [{ wch: 60 }];
    XLSX.utils.book_append_sheet(workbook, instructionSheet, '导入说明');

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx',
      compression: true
    });

    // 生成文件名（包含当前日期）
    const currentDate = new Date().toISOString().split('T')[0];
    const filename = `促销活动导入模板_${currentDate}.xlsx`;

    console.log('✅ Excel模板生成成功');

    // 返回文件
    return new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename*=UTF-8''${encodeURIComponent(filename)}`,
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 生成Excel模板失败:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'TEMPLATE_GENERATION_FAILED',
        message: '生成Excel模板失败',
        details: error instanceof Error ? error.message : '未知错误'
      }
    }, { status: 500 });
  }
}
