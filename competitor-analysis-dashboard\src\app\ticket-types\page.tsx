/**
 * 票种管理页面
 * 
 * 功能说明：
 * 1. 展示票种列表，支持分页、搜索、筛选
 * 2. 提供票种的增删改查功能
 * 3. 响应式设计，适配移动端
 * 4. 批量操作支持
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Edit, Trash2, Eye } from 'lucide-react';
import { MainLayout, PageContainer } from '@/components/layout/main-layout';
import { DataTable, ColumnDef } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { LoadingPage } from '@/components/ui/loading';
import { DeleteConfirmDialog } from '@/components/ui/confirm-dialog';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { TicketTypeForm } from '@/components/forms/ticket-type-form';
import { TicketType, PaginatedResult, CreateTicketTypeInput, UpdateTicketTypeInput } from '@/types';

// ============================================================================
// 页面状态接口定义
// ============================================================================

interface TicketTypesPageState {
  data: PaginatedResult<TicketType> | null;
  loading: boolean;
  error: string | null;
  searchText: string;
  currentPage: number;
  pageSize: number;
}

interface DialogState {
  type: 'create' | 'edit' | 'view' | null;
  open: boolean;
  data?: TicketType;
}

interface DeleteState {
  open: boolean;
  ticketType: TicketType | null;
}

// ============================================================================
// 票种管理页面组件
// ============================================================================

export default function TicketTypesPage() {
  // 页面状态
  const [state, setState] = useState<TicketTypesPageState>({
    data: null,
    loading: true,
    error: null,
    searchText: '',
    currentPage: 1,
    pageSize: 10
  });

  // 对话框状态
  const [dialogState, setDialogState] = useState<DialogState>({
    type: null,
    open: false
  });

  // 删除确认状态
  const [deleteState, setDeleteState] = useState<DeleteState>({
    open: false,
    ticketType: null
  });

  // 表单加载状态
  const [formLoading, setFormLoading] = useState(false);

  // 获取票种列表
  const fetchTicketTypes = async (page = state.currentPage, pageSize = state.pageSize, search = state.searchText) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString()
      });

      if (search.trim()) {
        params.append('ticket_type_name', search.trim());
      }

      const response = await fetch(`/api/ticket-types?${params}`);
      const result = await response.json();

      if (result.success) {
        setState(prev => ({
          ...prev,
          data: result.data,
          loading: false,
          currentPage: page,
          pageSize: pageSize,
          searchText: search
        }));
      } else {
        throw new Error(result.error?.message || '获取票种列表失败');
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '未知错误'
      }));
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchTicketTypes();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    fetchTicketTypes(1, state.pageSize, state.searchText);
  };

  // 分页处理
  const handlePageChange = (page: number, pageSize?: number) => {
    fetchTicketTypes(page, pageSize || state.pageSize, state.searchText);
  };

  // 打开对话框
  const openDialog = (type: 'create' | 'edit' | 'view', data?: TicketType) => {
    setDialogState({
      type,
      open: true,
      data
    });
  };

  // 关闭对话框
  const closeDialog = () => {
    setDialogState({
      type: null,
      open: false,
      data: undefined
    });
  };

  // 表单提交处理
  const handleFormSubmit = async (data: CreateTicketTypeInput | UpdateTicketTypeInput) => {
    try {
      setFormLoading(true);

      const isEdit = dialogState.type === 'edit';
      const url = isEdit ? `/api/ticket-types/${dialogState.data?.ticket_type_id}` : '/api/ticket-types';
      const method = isEdit ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (result.success) {
        closeDialog();
        fetchTicketTypes(); // 刷新列表
      } else {
        throw new Error(result.error?.message || '操作失败');
      }
    } catch (error) {
      throw error; // 让表单组件处理错误显示
    } finally {
      setFormLoading(false);
    }
  };

  // 删除票种
  const handleDelete = async (ticketType: TicketType) => {
    try {
      const response = await fetch(`/api/ticket-types/${ticketType.ticket_type_id}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        setDeleteState({ open: false, ticketType: null });
        fetchTicketTypes(); // 刷新列表
      } else {
        throw new Error(result.error?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除票种失败:', error);
      // 这里可以添加错误提示
    }
  };

  // 表格列定义
  const columns: ColumnDef<TicketType>[] = [
    {
      key: 'ticket_type_name',
      title: '票种名称',
      dataIndex: 'ticket_type_name',
      sorter: true,
      render: (value: string) => (
        <span className="font-medium text-gray-900">{value}</span>
      )
    },
    {
      key: 'category',
      title: '分类',
      dataIndex: 'category',
      render: (value: string | null) => (
        value ? (
          <Badge variant="secondary">{value}</Badge>
        ) : (
          <span className="text-gray-400">未分类</span>
        )
      )
    },
    {
      key: 'actions',
      title: '操作',
      width: 120,
      render: (_, record: TicketType) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => openDialog('view', record)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => openDialog('edit', record)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDeleteState({ open: true, ticketType: record })}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  // 加载状态
  if (state.loading && !state.data) {
    return (
      <MainLayout currentPath="/ticket-types">
        <LoadingPage text="加载票种数据..." />
      </MainLayout>
    );
  }

  // 错误状态
  if (state.error && !state.data) {
    return (
      <MainLayout currentPath="/ticket-types">
        <PageContainer>
          <div className="text-center py-12">
            <div className="text-red-500 text-lg font-medium mb-4">
              {state.error}
            </div>
            <Button onClick={() => fetchTicketTypes()}>
              重新加载
            </Button>
          </div>
        </PageContainer>
      </MainLayout>
    );
  }

  return (
    <MainLayout currentPath="/ticket-types">
      <PageContainer
        title="票种管理"
        description="管理票种信息，包括票种的基本信息和分类"
        actions={
          <Button onClick={() => openDialog('create')}>
            <Plus className="h-4 w-4 mr-2" />
            添加票种
          </Button>
        }
      >
        {/* 搜索和筛选 */}
        <div className="flex gap-4 mb-6">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="搜索票种名称..."
                value={state.searchText}
                onChange={(e) => setState(prev => ({ ...prev, searchText: e.target.value }))}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-9"
              />
            </div>
          </div>
          <Button onClick={handleSearch} variant="outline">
            搜索
          </Button>
        </div>

        {/* 数据表格 */}
        <DataTable
          columns={columns}
          data={state.data?.data || []}
          loading={state.loading}
          pagination={state.data ? {
            current: state.currentPage,
            pageSize: state.pageSize,
            total: state.data.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: handlePageChange
          } : undefined}
          emptyText="暂无票种数据"
        />

        {/* 表单对话框 */}
        <Dialog open={dialogState.open} onOpenChange={closeDialog}>
          <DialogContent className="max-w-2xl">
            <DialogTitle className="sr-only">
              {dialogState.type === 'create' && '创建票种'}
              {dialogState.type === 'edit' && '编辑票种'}
              {dialogState.type === 'view' && '查看票种'}
            </DialogTitle>
            <DialogDescription className="sr-only">
              {dialogState.type === 'create' && '创建新的票种信息'}
              {dialogState.type === 'edit' && '编辑现有票种信息'}
              {dialogState.type === 'view' && '查看票种详细信息'}
            </DialogDescription>
            {dialogState.type && (
              <TicketTypeForm
                mode={dialogState.type}
                initialData={dialogState.data}
                onSubmit={handleFormSubmit}
                onCancel={closeDialog}
                loading={formLoading}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* 删除确认对话框 */}
        <DeleteConfirmDialog
          open={deleteState.open}
          onOpenChange={(open) => setDeleteState(prev => ({ ...prev, open }))}
          itemName={deleteState.ticketType?.ticket_type_name}
          itemType="票种"
          onConfirm={() => deleteState.ticketType && handleDelete(deleteState.ticketType)}
        />
      </PageContainer>
    </MainLayout>
  );
}
