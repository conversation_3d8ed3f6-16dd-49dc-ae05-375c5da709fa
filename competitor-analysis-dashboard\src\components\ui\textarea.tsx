/**
 * Textarea 组件
 * 
 * 功能说明：
 * 1. 基于原生 textarea 元素的封装
 * 2. 支持多种尺寸和状态
 * 3. 与表单库兼容
 * 4. 响应式设计
 */

import * as React from "react";
import { cn } from "@/lib/utils";

// ============================================================================
// 组件属性接口
// ============================================================================

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /**
   * 组件尺寸
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * 是否显示错误状态
   */
  error?: boolean;
  
  /**
   * 是否可调整大小
   */
  resize?: 'none' | 'both' | 'horizontal' | 'vertical';
}

// ============================================================================
// Textarea 组件实现
// ============================================================================

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ 
    className, 
    size = 'md',
    error = false,
    resize = 'vertical',
    ...props 
  }, ref) => {
    // 尺寸样式映射
    const sizeClasses = {
      sm: 'min-h-[60px] px-2 py-1 text-xs',
      md: 'min-h-[80px] px-3 py-2 text-sm',
      lg: 'min-h-[100px] px-4 py-3 text-base'
    };

    // 调整大小样式映射
    const resizeClasses = {
      none: 'resize-none',
      both: 'resize',
      horizontal: 'resize-x',
      vertical: 'resize-y'
    };

    return (
      <textarea
        className={cn(
          // 基础样式
          "flex w-full rounded-md border border-input bg-background",
          "placeholder:text-muted-foreground",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:cursor-not-allowed disabled:opacity-50",
          
          // 尺寸样式
          sizeClasses[size],
          
          // 调整大小样式
          resizeClasses[resize],
          
          // 错误状态样式
          error && "border-destructive focus-visible:ring-destructive",
          
          // 自定义样式
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);

Textarea.displayName = "Textarea";

// ============================================================================
// 导出组件
// ============================================================================

export { Textarea };
