/**
 * 加载组件
 * 
 * 功能说明：
 * 1. 提供多种加载状态的显示
 * 2. 支持不同尺寸和样式
 * 3. 可配置加载文本和动画
 */

import React from 'react';
import { cn } from '@/lib/utils';

// ============================================================================
// 加载动画组件
// ============================================================================

/**
 * 旋转加载器组件
 */
export interface SpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const Spinner: React.FC<SpinnerProps> = ({ 
  size = 'md', 
  className 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',
        sizeClasses[size],
        className
      )}
    />
  );
};

/**
 * 脉冲加载器组件
 */
export interface PulseProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const Pulse: React.FC<PulseProps> = ({ 
  size = 'md', 
  className 
}) => {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'bg-blue-600 rounded-full animate-pulse',
            sizeClasses[size]
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  );
};

// ============================================================================
// 加载状态组件
// ============================================================================

/**
 * 内联加载组件
 */
export interface LoadingInlineProps {
  text?: string;
  size?: 'sm' | 'md' | 'lg';
  type?: 'spinner' | 'pulse';
  className?: string;
}

export const LoadingInline: React.FC<LoadingInlineProps> = ({
  text = '加载中...',
  size = 'md',
  type = 'spinner',
  className
}) => {
  const LoaderComponent = type === 'spinner' ? Spinner : Pulse;

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <LoaderComponent size={size} />
      {text && (
        <span className="text-sm text-gray-600 animate-pulse">
          {text}
        </span>
      )}
    </div>
  );
};

/**
 * 页面加载组件
 */
export interface LoadingPageProps {
  text?: string;
  description?: string;
  className?: string;
}

export const LoadingPage: React.FC<LoadingPageProps> = ({
  text = '加载中...',
  description,
  className
}) => {
  return (
    <div className={cn(
      'flex flex-col items-center justify-center min-h-[400px] space-y-4',
      className
    )}>
      <Spinner size="lg" />
      <div className="text-center space-y-2">
        <h3 className="text-lg font-medium text-gray-900">
          {text}
        </h3>
        {description && (
          <p className="text-sm text-gray-500 max-w-sm">
            {description}
          </p>
        )}
      </div>
    </div>
  );
};

/**
 * 覆盖层加载组件
 */
export interface LoadingOverlayProps {
  visible: boolean;
  text?: string;
  className?: string;
  children?: React.ReactNode;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  visible,
  text = '处理中...',
  className,
  children
}) => {
  if (!visible) {
    return <>{children}</>;
  }

  return (
    <div className={cn('relative', className)}>
      {children}
      <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-lg p-6 flex flex-col items-center space-y-4">
          <Spinner size="lg" />
          <p className="text-sm font-medium text-gray-900">
            {text}
          </p>
        </div>
      </div>
    </div>
  );
};

/**
 * 表格行加载组件
 */
export interface LoadingTableRowProps {
  columns: number;
  rows?: number;
  className?: string;
}

export const LoadingTableRow: React.FC<LoadingTableRowProps> = ({
  columns,
  rows = 5,
  className
}) => {
  return (
    <>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <tr key={rowIndex} className={className}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <td key={colIndex} className="px-4 py-3">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
            </td>
          ))}
        </tr>
      ))}
    </>
  );
};

/**
 * 卡片加载组件
 */
export interface LoadingCardProps {
  className?: string;
}

export const LoadingCard: React.FC<LoadingCardProps> = ({ className }) => {
  return (
    <div className={cn(
      'border rounded-lg p-6 space-y-4 animate-pulse',
      className
    )}>
      <div className="h-4 bg-gray-200 rounded w-3/4" />
      <div className="space-y-2">
        <div className="h-3 bg-gray-200 rounded" />
        <div className="h-3 bg-gray-200 rounded w-5/6" />
      </div>
      <div className="flex space-x-2">
        <div className="h-6 bg-gray-200 rounded w-16" />
        <div className="h-6 bg-gray-200 rounded w-20" />
      </div>
    </div>
  );
};

// ============================================================================
// 按钮加载状态组件
// ============================================================================

/**
 * 带加载状态的按钮组件
 */
export interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean;
  loadingText?: string;
  children: React.ReactNode;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  loading = false,
  loadingText,
  children,
  disabled,
  className,
  ...props
}) => {
  return (
    <button
      {...props}
      disabled={disabled || loading}
      className={cn(
        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        'disabled:pointer-events-none disabled:opacity-50',
        'bg-primary text-primary-foreground hover:bg-primary/90',
        'h-10 px-4 py-2',
        className
      )}
    >
      {loading && (
        <Spinner size="sm" className="mr-2" />
      )}
      {loading && loadingText ? loadingText : children}
    </button>
  );
};

// ============================================================================
// 导出所有组件
// ============================================================================

export {
  Spinner,
  Pulse,
  LoadingInline,
  LoadingPage,
  LoadingOverlay,
  LoadingTableRow,
  LoadingCard,
  LoadingButton
};
