/**
 * API相关的TypeScript类型定义
 * 
 * 功能说明：
 * 1. 定义API请求和响应的数据结构
 * 2. 提供统一的错误处理类型
 * 3. 支持API状态管理和加载状态
 */

import { 
  Competitor, 
  TicketType, 
  Promotion, 
  PromotionDetail,
  DailySales, 
  DailyUsage,
  PaginatedResult,
  PaginationParams
} from './database';

// ============================================================================
// 基础API类型定义
// ============================================================================

/**
 * HTTP方法枚举
 */
export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE'
}

/**
 * API错误类型枚举
 */
export enum ApiErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',           // 网络错误
  VALIDATION_ERROR = 'VALIDATION_ERROR',     // 验证错误
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR', // 认证错误
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',   // 授权错误
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',       // 资源不存在
  SERVER_ERROR = 'SERVER_ERROR',             // 服务器错误
  DATABASE_ERROR = 'DATABASE_ERROR',         // 数据库错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'            // 未知错误
}

/**
 * API错误详情接口
 */
export interface ApiError {
  type: ApiErrorType;        // 错误类型
  message: string;           // 错误消息
  code?: string | number;    // 错误代码
  details?: any;            // 错误详情
  timestamp: string;        // 错误时间戳
}

/**
 * 统一API响应接口
 */
export interface ApiResponse<T = any> {
  success: boolean;          // 请求是否成功
  data?: T;                 // 响应数据
  message?: string;         // 响应消息
  error?: ApiError;         // 错误信息
  timestamp: string;        // 响应时间戳
}

/**
 * API请求状态枚举
 */
export enum ApiStatus {
  IDLE = 'idle',            // 空闲状态
  LOADING = 'loading',      // 加载中
  SUCCESS = 'success',      // 成功
  ERROR = 'error'           // 错误
}

/**
 * API请求状态接口
 */
export interface ApiState<T = any> {
  status: ApiStatus;        // 请求状态
  data?: T;                // 响应数据
  error?: ApiError;        // 错误信息
  loading: boolean;        // 是否正在加载
}

// ============================================================================
// 竞品相关API类型定义
// ============================================================================

/**
 * 获取竞品列表的API响应
 */
export type GetCompetitorsResponse = ApiResponse<PaginatedResult<Competitor>>;

/**
 * 获取单个竞品的API响应
 */
export type GetCompetitorResponse = ApiResponse<Competitor>;

/**
 * 创建竞品的API请求体
 */
export interface CreateCompetitorRequest {
  competitor_name: string;
  city?: string;
  park_type?: string;
  is_active?: boolean;
}

/**
 * 创建竞品的API响应
 */
export type CreateCompetitorResponse = ApiResponse<Competitor>;

/**
 * 更新竞品的API请求体
 */
export interface UpdateCompetitorRequest {
  competitor_name?: string;
  city?: string;
  park_type?: string;
  is_active?: boolean;
}

/**
 * 更新竞品的API响应
 */
export type UpdateCompetitorResponse = ApiResponse<Competitor>;

/**
 * 删除竞品的API响应
 */
export type DeleteCompetitorResponse = ApiResponse<{ deleted: boolean }>;

// ============================================================================
// 票种相关API类型定义
// ============================================================================

/**
 * 获取票种列表的API响应
 */
export type GetTicketTypesResponse = ApiResponse<PaginatedResult<TicketType>>;

/**
 * 获取单个票种的API响应
 */
export type GetTicketTypeResponse = ApiResponse<TicketType>;

/**
 * 创建票种的API请求体
 */
export interface CreateTicketTypeRequest {
  ticket_type_name: string;
  category?: string;
}

/**
 * 创建票种的API响应
 */
export type CreateTicketTypeResponse = ApiResponse<TicketType>;

/**
 * 更新票种的API请求体
 */
export interface UpdateTicketTypeRequest {
  ticket_type_name?: string;
  category?: string;
}

/**
 * 更新票种的API响应
 */
export type UpdateTicketTypeResponse = ApiResponse<TicketType>;

/**
 * 删除票种的API响应
 */
export type DeleteTicketTypeResponse = ApiResponse<{ deleted: boolean }>;

// ============================================================================
// 促销活动相关API类型定义
// ============================================================================

/**
 * 获取促销活动列表的API响应
 */
export type GetPromotionsResponse = ApiResponse<PaginatedResult<PromotionDetail>>;

/**
 * 获取单个促销活动的API响应
 */
export type GetPromotionResponse = ApiResponse<PromotionDetail>;

/**
 * 创建促销活动的API请求体
 */
export interface CreatePromotionRequest {
  competitor_id: number;
  ticket_type_id: number;
  activity_name: string;
  rack_rate?: number;
  promo_price?: number;
  sale_start_date?: string;
  sale_end_date?: string;
  use_start_date?: string;
  use_end_date?: string;
  sales_channel?: string;
  usage_rules?: string;
  data_source_url?: string;
  remarks?: string;
}

/**
 * 创建促销活动的API响应
 */
export type CreatePromotionResponse = ApiResponse<Promotion>;

/**
 * 更新促销活动的API请求体
 */
export interface UpdatePromotionRequest {
  competitor_id?: number;
  ticket_type_id?: number;
  activity_name?: string;
  rack_rate?: number;
  promo_price?: number;
  sale_start_date?: string;
  sale_end_date?: string;
  use_start_date?: string;
  use_end_date?: string;
  sales_channel?: string;
  usage_rules?: string;
  data_source_url?: string;
  remarks?: string;
}

/**
 * 更新促销活动的API响应
 */
export type UpdatePromotionResponse = ApiResponse<Promotion>;

/**
 * 删除促销活动的API响应
 */
export type DeletePromotionResponse = ApiResponse<{ deleted: boolean }>;

// ============================================================================
// 日历相关API类型定义
// ============================================================================

/**
 * 获取销售日历列表的API响应
 */
export type GetDailySalesResponse = ApiResponse<PaginatedResult<DailySales>>;

/**
 * 创建销售日历记录的API请求体
 */
export interface CreateDailySalesRequest {
  sales_date: string;
  promotion_id: number;
  promo_price?: number;
  rack_rate?: number;
  discount_rate?: number;
}

/**
 * 创建销售日历记录的API响应
 */
export type CreateDailySalesResponse = ApiResponse<DailySales>;

/**
 * 获取使用日历列表的API响应
 */
export type GetDailyUsageResponse = ApiResponse<PaginatedResult<DailyUsage>>;

/**
 * 创建使用日历记录的API请求体
 */
export interface CreateDailyUsageRequest {
  usage_date: string;
  promotion_id: number;
  promo_price?: number;
  rack_rate?: number;
  discount_rate?: number;
}

/**
 * 创建使用日历记录的API响应
 */
export type CreateDailyUsageResponse = ApiResponse<DailyUsage>;

// ============================================================================
// 查询和筛选相关API类型定义
// ============================================================================

/**
 * 通用查询参数接口
 */
export interface QueryParams extends PaginationParams {
  search?: string;           // 搜索关键词
  filters?: Record<string, any>; // 筛选条件
}

/**
 * 促销活动查询参数接口
 */
export interface PromotionQueryParams extends QueryParams {
  competitor_id?: number;
  ticket_type_id?: number;
  activity_name?: string;
  sales_channel?: string;
  sale_date_start?: string;
  sale_date_end?: string;
  use_date_start?: string;
  use_date_end?: string;
  min_price?: number;
  max_price?: number;
}

/**
 * 竞品查询参数接口
 */
export interface CompetitorQueryParams extends QueryParams {
  competitor_name?: string;
  city?: string;
  park_type?: string;
  is_active?: boolean;
}

/**
 * 票种查询参数接口
 */
export interface TicketTypeQueryParams extends QueryParams {
  ticket_type_name?: string;
  category?: string;
}

// ============================================================================
// 批量操作相关API类型定义
// ============================================================================

/**
 * 批量删除请求体
 */
export interface BatchDeleteRequest {
  ids: (string | number)[];  // 要删除的记录ID列表
}

/**
 * 批量删除响应
 */
export interface BatchDeleteResponse {
  success: boolean;
  deleted_count: number;     // 成功删除的数量
  failed_count: number;      // 删除失败的数量
  errors?: string[];         // 错误信息列表
}

/**
 * 批量操作的API响应
 */
export type BatchOperationResponse = ApiResponse<BatchDeleteResponse>;

// ============================================================================
// 统计和分析相关API类型定义
// ============================================================================

/**
 * 统计数据接口
 */
export interface StatisticsData {
  total_competitors: number;     // 竞品总数
  total_ticket_types: number;    // 票种总数
  total_promotions: number;      // 促销活动总数
  active_promotions: number;     // 活跃促销活动数
  avg_discount_rate: number;     // 平均折扣率
  top_competitors: Array<{       // 热门竞品
    competitor_name: string;
    promotion_count: number;
  }>;
  top_ticket_types: Array<{      // 热门票种
    ticket_type_name: string;
    promotion_count: number;
  }>;
}

/**
 * 获取统计数据的API响应
 */
export type GetStatisticsResponse = ApiResponse<StatisticsData>;

// ============================================================================
// 导入导出相关API类型定义
// ============================================================================

/**
 * 导出数据请求参数
 */
export interface ExportRequest {
  format: 'csv' | 'excel' | 'json'; // 导出格式
  filters?: Record<string, any>;     // 筛选条件
  fields?: string[];                 // 要导出的字段
}

/**
 * 导出数据响应
 */
export interface ExportResponse {
  download_url: string;              // 下载链接
  filename: string;                  // 文件名
  expires_at: string;                // 过期时间
}

/**
 * 导出数据的API响应
 */
export type ExportDataResponse = ApiResponse<ExportResponse>;

/**
 * 导入数据结果
 */
export interface ImportResult {
  success_count: number;             // 成功导入数量
  failure_count: number;             // 导入失败数量
  errors?: Array<{                   // 错误详情
    row: number;
    message: string;
  }>;
}

/**
 * 导入数据的API响应
 */
export type ImportDataResponse = ApiResponse<ImportResult>;
