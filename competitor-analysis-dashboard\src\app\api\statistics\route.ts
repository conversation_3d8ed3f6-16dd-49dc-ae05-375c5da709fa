/**
 * 统计API路由
 * 
 * 功能说明：
 * 1. 提供系统整体统计信息
 * 2. 支持各种维度的数据分析
 * 3. 提供仪表板所需的统计数据
 */

import { NextRequest } from 'next/server';
import { daoManager } from '@/lib/dao';
import {
  createSuccessResponse,
  withErrorHandling,
  createMethodNotAllowedResponse
} from '@/lib/api-utils';

/**
 * GET /api/statistics
 * 获取系统整体统计信息
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('📥 GET /api/statistics - 获取系统统计信息');

  // 获取系统整体统计
  const systemStats = await daoManager.getSystemStatistics();

  console.log('✅ 成功获取系统统计信息');
  return createSuccessResponse(systemStats, '获取统计信息成功');
});

/**
 * 处理不支持的HTTP方法
 */
export async function POST(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET']);
}

export async function PUT(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET']);
}

export async function DELETE(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET']);
}

export async function PATCH(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET']);
}
