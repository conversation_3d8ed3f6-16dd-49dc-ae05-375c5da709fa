# 竞品分析管理系统

一个基于 Next.js 15 + TypeScript + MySQL 的现代化竞品分析管理系统。

## 🚀 功能特性

- **竞品管理**：完整的竞品信息CRUD操作
- **票种管理**：票种分类和信息管理
- **促销活动管理**：复杂的促销活动数据管理
- **数据统计**：实时的数据统计和分析
- **响应式设计**：完美适配桌面和移动端
- **现代化UI**：基于shadcn/ui的美观界面

## 🛠️ 技术栈

- **前端框架**：Next.js 15 (App Router)
- **开发语言**：TypeScript
- **样式框架**：Tailwind CSS v4
- **UI组件库**：shadcn/ui
- **数据库**：MySQL 8.0
- **表单处理**：React Hook Form + Zod
- **图标库**：Lucide React

## 📋 系统要求

- Node.js 18 或更高版本
- MySQL 8.0 或更高版本
- npm 或 yarn 包管理器

## 🔧 安装和配置

### 1. 安装依赖

```bash
npm install
```

### 2. 数据库配置

#### 2.1 创建数据库

在MySQL中执行以下命令创建数据库：

```sql
CREATE DATABASE competitor_analysis_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 2.2 创建数据表

运行项目根目录下的SQL文件：

```bash
mysql -u root -p competitor_analysis_db < ../create_table.sql
```

#### 2.3 配置环境变量

复制环境变量示例文件：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，填入你的数据库配置：

```env
# 数据库连接信息
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=你的MySQL密码
DB_NAME=competitor_analysis_db
```

### 3. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 🧪 测试功能

访问 [http://localhost:3000/test](http://localhost:3000/test) 进行系统测试。

## 📁 主要页面

- **首页仪表板**：`/` - 系统概览和统计信息
- **竞品管理**：`/competitors` - 竞品信息管理
- **系统测试**：`/test` - 功能测试页面

## 🔌 API接口

### 竞品相关
- `GET /api/competitors` - 获取竞品列表
- `POST /api/competitors` - 创建竞品
- `GET /api/competitors/[id]` - 获取单个竞品
- `PUT /api/competitors/[id]` - 更新竞品
- `DELETE /api/competitors/[id]` - 删除竞品

### 统计相关
- `GET /api/statistics` - 获取系统统计信息
