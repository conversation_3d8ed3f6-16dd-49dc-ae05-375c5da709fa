/**
 * 竞品数据访问对象(DAO)类
 * 
 * 功能说明：
 * 1. 实现竞品表的CRUD操作
 * 2. 提供竞品特有的查询方法
 * 3. 支持按城市、园区类型等条件筛选
 * 4. 提供竞品活跃状态管理
 */

import { BaseDAO } from './base-dao';
import { executeQuery } from '../database';
import { 
  Competitor, 
  CreateCompetitorInput, 
  UpdateCompetitorInput,
  CompetitorFilters,
  PaginationParams,
  PaginatedResult
} from '@/types';

/**
 * 竞品DAO类
 * 继承基础DAO类，实现竞品特有的数据操作
 */
export class CompetitorDAO extends BaseDAO<Competitor, CreateCompetitorInput, UpdateCompetitorInput> {
  constructor() {
    super('Competitors', 'competitor_id');
  }

  /**
   * 根据筛选条件查询竞品列表（分页）
   * @param params 分页参数
   * @param filters 筛选条件
   * @returns 分页查询结果
   */
  async findWithFilters(
    params: PaginationParams,
    filters: CompetitorFilters = {}
  ): Promise<PaginatedResult<Competitor>> {
    try {
      console.log('🔍 根据筛选条件查询竞品列表');
      console.log('📝 筛选条件:', filters);

      const whereConditions: string[] = [];
      const whereParams: any[] = [];

      // 按竞品名称搜索
      if (filters.competitor_name) {
        whereConditions.push('competitor_name LIKE ?');
        whereParams.push(`%${filters.competitor_name}%`);
      }

      // 按城市筛选
      if (filters.city) {
        whereConditions.push('city = ?');
        whereParams.push(filters.city);
      }

      // 按园区类型筛选
      if (filters.park_type) {
        whereConditions.push('park_type = ?');
        whereParams.push(filters.park_type);
      }

      // 按活跃状态筛选
      if (filters.is_active !== undefined) {
        whereConditions.push('is_active = ?');
        whereParams.push(filters.is_active);
      }

      const whereClause = whereConditions.length > 0 ? whereConditions.join(' AND ') : '';

      return await this.findWithPagination(params, whereClause, whereParams);
    } catch (error) {
      console.error('❌ 根据筛选条件查询竞品失败:', error);
      throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据竞品名称查询（精确匹配）
   * @param competitorName 竞品名称
   * @returns 竞品信息
   */
  async findByName(competitorName: string): Promise<Competitor | null> {
    try {
      console.log(`🔍 查询竞品名称为"${competitorName}"的记录`);

      const query = 'SELECT * FROM Competitors WHERE competitor_name = ?';
      const results = await executeQuery<Competitor>(query, [competitorName]);

      if (results.length === 0) {
        console.log(`⚠️ 未找到名称为"${competitorName}"的竞品`);
        return null;
      }

      console.log('✅ 成功查询到竞品信息');
      return results[0];
    } catch (error) {
      console.error('❌ 根据名称查询竞品失败:', error);
      throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取所有活跃的竞品
   * @returns 活跃竞品列表
   */
  async findActiveCompetitors(): Promise<Competitor[]> {
    try {
      console.log('🔍 查询所有活跃的竞品');

      const query = `
        SELECT * FROM Competitors 
        WHERE is_active = true 
        ORDER BY competitor_name ASC
      `;
      const results = await executeQuery<Competitor>(query);

      console.log(`✅ 成功查询到${results.length}个活跃竞品`);
      return results;
    } catch (error) {
      console.error('❌ 查询活跃竞品失败:', error);
      throw new Error(`查询活跃竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据城市获取竞品列表
   * @param city 城市名称
   * @returns 竞品列表
   */
  async findByCity(city: string): Promise<Competitor[]> {
    try {
      console.log(`🔍 查询城市"${city}"的竞品列表`);

      const query = `
        SELECT * FROM Competitors 
        WHERE city = ? 
        ORDER BY competitor_name ASC
      `;
      const results = await executeQuery<Competitor>(query, [city]);

      console.log(`✅ 成功查询到${results.length}个竞品`);
      return results;
    } catch (error) {
      console.error('❌ 根据城市查询竞品失败:', error);
      throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据园区类型获取竞品列表
   * @param parkType 园区类型
   * @returns 竞品列表
   */
  async findByParkType(parkType: string): Promise<Competitor[]> {
    try {
      console.log(`🔍 查询园区类型"${parkType}"的竞品列表`);

      const query = `
        SELECT * FROM Competitors 
        WHERE park_type = ? 
        ORDER BY competitor_name ASC
      `;
      const results = await executeQuery<Competitor>(query, [parkType]);

      console.log(`✅ 成功查询到${results.length}个竞品`);
      return results;
    } catch (error) {
      console.error('❌ 根据园区类型查询竞品失败:', error);
      throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取所有城市列表
   * @returns 城市列表
   */
  async getAllCities(): Promise<string[]> {
    try {
      console.log('🔍 获取所有城市列表');

      const query = `
        SELECT DISTINCT city 
        FROM Competitors 
        WHERE city IS NOT NULL AND city != '' 
        ORDER BY city ASC
      `;
      const results = await executeQuery<{ city: string }>(query);

      const cities = results.map(row => row.city);
      console.log(`✅ 成功获取${cities.length}个城市`);
      return cities;
    } catch (error) {
      console.error('❌ 获取城市列表失败:', error);
      throw new Error(`获取城市列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取所有园区类型列表
   * @returns 园区类型列表
   */
  async getAllParkTypes(): Promise<string[]> {
    try {
      console.log('🔍 获取所有园区类型列表');

      const query = `
        SELECT DISTINCT park_type 
        FROM Competitors 
        WHERE park_type IS NOT NULL AND park_type != '' 
        ORDER BY park_type ASC
      `;
      const results = await executeQuery<{ park_type: string }>(query);

      const parkTypes = results.map(row => row.park_type);
      console.log(`✅ 成功获取${parkTypes.length}个园区类型`);
      return parkTypes;
    } catch (error) {
      console.error('❌ 获取园区类型列表失败:', error);
      throw new Error(`获取园区类型列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 更新竞品活跃状态
   * @param competitorId 竞品ID
   * @param isActive 是否活跃
   * @returns 是否更新成功
   */
  async updateActiveStatus(competitorId: number, isActive: boolean): Promise<boolean> {
    try {
      console.log(`🔄 更新竞品${competitorId}的活跃状态为${isActive ? '活跃' : '非活跃'}`);

      const query = 'UPDATE Competitors SET is_active = ? WHERE competitor_id = ?';
      const results = await executeQuery(query, [isActive, competitorId]);
      const affectedRows = (results as any).affectedRows;

      if (affectedRows === 0) {
        console.log(`⚠️ 未找到ID为${competitorId}的竞品`);
        return false;
      }

      console.log('✅ 成功更新竞品活跃状态');
      return true;
    } catch (error) {
      console.error('❌ 更新竞品活跃状态失败:', error);
      throw new Error(`更新活跃状态失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 批量更新竞品活跃状态
   * @param competitorIds 竞品ID列表
   * @param isActive 是否活跃
   * @returns 更新的记录数量
   */
  async batchUpdateActiveStatus(competitorIds: number[], isActive: boolean): Promise<number> {
    try {
      if (competitorIds.length === 0) {
        console.log('⚠️ 没有需要更新的竞品');
        return 0;
      }

      console.log(`🔄 批量更新${competitorIds.length}个竞品的活跃状态`);

      const placeholders = competitorIds.map(() => '?').join(', ');
      const query = `
        UPDATE Competitors 
        SET is_active = ? 
        WHERE competitor_id IN (${placeholders})
      `;

      const results = await executeQuery(query, [isActive, ...competitorIds]);
      const affectedRows = (results as any).affectedRows;

      console.log(`✅ 成功更新${affectedRows}个竞品的活跃状态`);
      return affectedRows;
    } catch (error) {
      console.error('❌ 批量更新竞品活跃状态失败:', error);
      throw new Error(`批量更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 检查竞品名称是否已存在
   * @param competitorName 竞品名称
   * @param excludeId 排除的竞品ID（用于更新时检查）
   * @returns 是否已存在
   */
  async isNameExists(competitorName: string, excludeId?: number): Promise<boolean> {
    try {
      let query = 'SELECT 1 FROM Competitors WHERE competitor_name = ?';
      const params: any[] = [competitorName];

      if (excludeId) {
        query += ' AND competitor_id != ?';
        params.push(excludeId);
      }

      const results = await executeQuery(query, params);
      return results.length > 0;
    } catch (error) {
      console.error('❌ 检查竞品名称是否存在失败:', error);
      throw new Error(`检查名称失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取竞品统计信息
   * @returns 统计信息
   */
  async getStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    cityCount: number;
    parkTypeCount: number;
  }> {
    try {
      console.log('📊 获取竞品统计信息');

      const queries = [
        'SELECT COUNT(*) as total FROM Competitors',
        'SELECT COUNT(*) as active FROM Competitors WHERE is_active = true',
        'SELECT COUNT(*) as inactive FROM Competitors WHERE is_active = false',
        'SELECT COUNT(DISTINCT city) as cityCount FROM Competitors WHERE city IS NOT NULL AND city != ""',
        'SELECT COUNT(DISTINCT park_type) as parkTypeCount FROM Competitors WHERE park_type IS NOT NULL AND park_type != ""'
      ];

      const [totalResult, activeResult, inactiveResult, cityResult, parkTypeResult] = await Promise.all(
        queries.map(query => executeQuery<{ [key: string]: number }>(query))
      );

      const statistics = {
        total: totalResult[0].total,
        active: activeResult[0].active,
        inactive: inactiveResult[0].inactive,
        cityCount: cityResult[0].cityCount,
        parkTypeCount: parkTypeResult[0].parkTypeCount
      };

      console.log('✅ 成功获取竞品统计信息:', statistics);
      return statistics;
    } catch (error) {
      console.error('❌ 获取竞品统计信息失败:', error);
      throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

// 导出单例实例
export const competitorDAO = new CompetitorDAO();
