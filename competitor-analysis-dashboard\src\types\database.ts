/**
 * 数据库表结构对应的TypeScript类型定义
 * 
 * 功能说明：
 * 1. 定义所有数据库表的接口类型
 * 2. 提供类型安全的数据操作
 * 3. 支持前端表单验证和数据展示
 */

// ============================================================================
// 基础类型定义
// ============================================================================

/**
 * 数据库记录的基础接口
 * 包含所有表都可能具有的通用字段
 */
export interface BaseRecord {
  created_at?: Date;
  updated_at?: Date;
}

/**
 * 分页查询参数接口
 */
export interface PaginationParams {
  page: number;          // 当前页码（从1开始）
  pageSize: number;      // 每页记录数
  sortBy?: string;       // 排序字段
  sortOrder?: 'ASC' | 'DESC'; // 排序方向
}

/**
 * 分页查询结果接口
 */
export interface PaginatedResult<T> {
  data: T[];             // 当前页数据
  total: number;         // 总记录数
  page: number;          // 当前页码
  pageSize: number;      // 每页记录数
  totalPages: number;    // 总页数
}

/**
 * API响应基础接口
 */
export interface ApiResponse<T = any> {
  success: boolean;      // 操作是否成功
  data?: T;             // 返回的数据
  message?: string;     // 响应消息
  error?: string;       // 错误信息
}

// ============================================================================
// 竞品字典表 (Competitors) 类型定义
// ============================================================================

/**
 * 竞品信息接口
 * 对应数据库表：Competitors
 */
export interface Competitor extends BaseRecord {
  competitor_id: number;        // 竞品ID（主键，自增）
  competitor_name: string;      // 竞品名称
  city?: string;               // 所在城市
  park_type?: string;          // 园区类型
  is_active: boolean;          // 是否活跃状态
}

/**
 * 创建竞品时的输入接口
 * 排除自动生成的字段
 */
export interface CreateCompetitorInput {
  competitor_name: string;      // 竞品名称（必填）
  city?: string;               // 所在城市（可选）
  park_type?: string;          // 园区类型（可选）
  is_active?: boolean;         // 是否活跃（默认true）
}

/**
 * 更新竞品时的输入接口
 * 所有字段都是可选的
 */
export interface UpdateCompetitorInput {
  competitor_name?: string;     // 竞品名称
  city?: string;               // 所在城市
  park_type?: string;          // 园区类型
  is_active?: boolean;         // 是否活跃状态
}

// ============================================================================
// 票种字典表 (TicketTypes) 类型定义
// ============================================================================

/**
 * 票种信息接口
 * 对应数据库表：TicketTypes
 */
export interface TicketType extends BaseRecord {
  ticket_type_id: number;       // 票种ID（主键，自增）
  ticket_type_name: string;     // 票种名称
  category?: string;           // 票种分类
}

/**
 * 创建票种时的输入接口
 */
export interface CreateTicketTypeInput {
  ticket_type_name: string;     // 票种名称（必填）
  category?: string;           // 票种分类（可选）
}

/**
 * 更新票种时的输入接口
 */
export interface UpdateTicketTypeInput {
  ticket_type_name?: string;    // 票种名称
  category?: string;           // 票种分类
}

// ============================================================================
// 促销活动主表 (Promotions) 类型定义
// ============================================================================

/**
 * 使用规则字典表接口
 * 对应数据库表：UsageRules
 */
export interface UsageRule extends BaseRecord {
  rule_id: number;             // 规则ID（主键，自增）
  rule_description: string;    // 规则描述
  is_active: boolean;          // 是否启用
}

/**
 * 促销活动信息接口
 * 对应数据库表：Promotions
 */
export interface Promotion extends BaseRecord {
  promotion_id: number;         // 促销活动ID（主键，自增）
  competitor_id: number;        // 竞品ID（外键）
  ticket_type_id: number;       // 票种ID（外键）
  activity_name: string;        // 活动名称
  rack_rate?: number;          // 门市价
  promo_price?: number;        // 促销价
  sale_start_date?: Date;      // 销售开始日期
  sale_end_date?: Date;        // 销售结束日期
  use_start_date?: Date;       // 使用开始日期
  use_end_date?: Date;         // 使用结束日期
  sales_channel?: string;      // 销售渠道
  usage_rule_id: number;       // 使用规则ID（外键，必填）
  data_source_url?: string;    // 数据来源URL
  entry_date: Date;            // 录入日期
  remarks?: string;            // 备注
}

/**
 * 促销活动详细信息接口（包含关联数据）
 * 用于展示页面，包含竞品和票种的详细信息
 */
export interface PromotionDetail extends Promotion {
  competitor?: Competitor;      // 关联的竞品信息
  ticket_type?: TicketType;    // 关联的票种信息
  usage_rule?: UsageRule;       // 关联的使用规则信息
  discount_rate?: number;      // 计算得出的折扣率
}

/**
 * 创建使用规则时的输入接口
 */
export interface CreateUsageRuleInput {
  rule_description: string;     // 规则描述（必填）
  is_active?: boolean;         // 是否启用（默认true）
}

/**
 * 更新使用规则时的输入接口
 */
export interface UpdateUsageRuleInput {
  rule_description?: string;    // 规则描述
  is_active?: boolean;         // 是否启用
}

/**
 * 创建促销活动时的输入接口
 */
export interface CreatePromotionInput {
  competitor_id: number;        // 竞品ID（必填）
  ticket_type_id: number;       // 票种ID（必填）
  activity_name: string;        // 活动名称（必填）
  rack_rate?: number;          // 门市价
  promo_price?: number;        // 促销价
  sale_start_date?: string;    // 销售开始日期（ISO字符串）
  sale_end_date?: string;      // 销售结束日期（ISO字符串）
  use_start_date?: string;     // 使用开始日期（ISO字符串）
  use_end_date?: string;       // 使用结束日期（ISO字符串）
  sales_channel?: string;      // 销售渠道
  usage_rule_id: number;       // 使用规则ID（外键，必填）
  data_source_url?: string;    // 数据来源URL
  remarks?: string;            // 备注
}

/**
 * 更新促销活动时的输入接口
 */
export interface UpdatePromotionInput {
  competitor_id?: number;       // 竞品ID
  ticket_type_id?: number;      // 票种ID
  activity_name?: string;       // 活动名称
  rack_rate?: number;          // 门市价
  promo_price?: number;        // 促销价
  sale_start_date?: string;    // 销售开始日期
  sale_end_date?: string;      // 销售结束日期
  use_start_date?: string;     // 使用开始日期
  use_end_date?: string;       // 使用结束日期
  sales_channel?: string;      // 销售渠道
  usage_rule_id?: number;      // 使用规则ID（外键）
  data_source_url?: string;    // 数据来源URL
  remarks?: string;            // 备注
}

/**
 * 批量导入促销活动的输入接口
 */
export interface BulkImportPromotionInput {
  competitor_name: string;      // 竞品名称（用于查找competitor_id）
  ticket_type_name: string;     // 票种名称（用于查找ticket_type_id）
  activity_name: string;        // 活动名称（必填）
  rack_rate?: number;          // 门市价
  promo_price?: number;        // 促销价
  sale_start_date?: string;    // 销售开始日期（YYYY-MM-DD格式）
  sale_end_date?: string;      // 销售结束日期（YYYY-MM-DD格式）
  use_start_date?: string;     // 使用开始日期（YYYY-MM-DD格式）
  use_end_date?: string;       // 使用结束日期（YYYY-MM-DD格式）
  sales_channel?: string;      // 销售渠道
  usage_rules?: string;        // 使用规则（兼容旧格式，将映射到usage_rule_id）
  data_source_url?: string;    // 数据来源URL
  remarks?: string;            // 备注
}

/**
 * 批量导入结果接口
 */
export interface BulkImportResult {
  total_rows: number;          // 总行数
  success_count: number;       // 成功导入数量
  error_count: number;         // 失败数量
  errors: BulkImportError[];   // 错误详情列表
  imported_ids: number[];      // 成功导入的促销活动ID列表
}

/**
 * 批量导入错误信息接口
 */
export interface BulkImportError {
  row_number: number;          // 错误行号（从1开始）
  field?: string;              // 错误字段名
  error_message: string;       // 错误信息
  row_data?: any;              // 错误行的原始数据
}

/**
 * Excel模板列定义接口
 */
export interface ExcelTemplateColumn {
  field: string;               // 字段名
  header: string;              // 列标题
  required: boolean;           // 是否必填
  type: 'string' | 'number' | 'date'; // 数据类型
  example?: string;            // 示例值
  description?: string;        // 字段说明
}

// ============================================================================
// 销售日历表 (DailySales) 类型定义
// ============================================================================

/**
 * 销售日历信息接口
 * 对应数据库表：DailySales
 */
export interface DailySales extends BaseRecord {
  sales_date: Date;            // 销售日期（复合主键）
  promotion_id: number;        // 促销活动ID（复合主键，外键）
  promo_price?: number;        // 促销价
  rack_rate?: number;          // 门市价
  discount_rate?: number;      // 折扣率
}

/**
 * 销售日历详细信息接口（包含关联数据）
 */
export interface DailySalesDetail extends DailySales {
  promotion?: PromotionDetail; // 关联的促销活动信息
}

/**
 * 创建销售日历记录的输入接口
 */
export interface CreateDailySalesInput {
  sales_date: string;          // 销售日期（ISO字符串）
  promotion_id: number;        // 促销活动ID
  promo_price?: number;        // 促销价
  rack_rate?: number;          // 门市价
  discount_rate?: number;      // 折扣率
}

// ============================================================================
// 使用日历表 (DailyUsage) 类型定义
// ============================================================================

/**
 * 使用日历信息接口
 * 对应数据库表：DailyUsage
 */
export interface DailyUsage extends BaseRecord {
  usage_date: Date;            // 使用日期（复合主键）
  promotion_id: number;        // 促销活动ID（复合主键，外键）
  promo_price?: number;        // 促销价
  rack_rate?: number;          // 门市价
  discount_rate?: number;      // 折扣率
}

/**
 * 使用日历详细信息接口（包含关联数据）
 */
export interface DailyUsageDetail extends DailyUsage {
  promotion?: PromotionDetail; // 关联的促销活动信息
}

/**
 * 创建使用日历记录的输入接口
 */
export interface CreateDailyUsageInput {
  usage_date: string;          // 使用日期（ISO字符串）
  promotion_id: number;        // 促销活动ID
  promo_price?: number;        // 促销价
  rack_rate?: number;          // 门市价
  discount_rate?: number;      // 折扣率
}

// ============================================================================
// 查询和筛选相关类型定义
// ============================================================================

/**
 * 促销活动查询筛选条件接口
 */
export interface PromotionFilters {
  competitor_id?: number;       // 按竞品筛选
  ticket_type_id?: number;      // 按票种筛选
  activity_name?: string;       // 按活动名称搜索
  sales_channel?: string;       // 按销售渠道筛选
  sale_date_start?: string;     // 销售开始日期范围
  sale_date_end?: string;       // 销售结束日期范围
  use_date_start?: string;      // 使用开始日期范围
  use_date_end?: string;        // 使用结束日期范围
  min_price?: number;          // 最低价格
  max_price?: number;          // 最高价格
}

/**
 * 竞品查询筛选条件接口
 */
export interface CompetitorFilters {
  competitor_name?: string;     // 按竞品名称搜索
  city?: string;               // 按城市筛选
  park_type?: string;          // 按园区类型筛选
  is_active?: boolean;         // 按活跃状态筛选
}

/**
 * 票种查询筛选条件接口
 */
export interface TicketTypeFilters {
  ticket_type_name?: string;    // 按票种名称搜索
  category?: string;           // 按分类筛选
}
