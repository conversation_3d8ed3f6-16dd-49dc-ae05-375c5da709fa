/**
 * 票种API路由
 * 
 * 功能说明：
 * 1. 处理票种的CRUD操作
 * 2. 支持分页查询和筛选
 * 3. 提供数据验证和错误处理
 * 4. 实现RESTful API规范
 */

import { NextRequest } from 'next/server';
import { ticketTypeDAO } from '@/lib/dao';
import {
  createSuccessResponse,
  createValidationErrorResponse,
  parsePaginationParams,
  parseQueryParams,
  parseRequestBody,
  validateRequiredFields,
  validateStringLength,
  withErrorHandling,
  createMethodNotAllowedResponse
} from '@/lib/api-utils';
import { CreateTicketTypeInput, TicketTypeFilters } from '@/types';

/**
 * GET /api/ticket-types
 * 获取票种列表（支持分页和筛选）
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('📥 GET /api/ticket-types - 获取票种列表');

  // 解析分页参数
  const paginationParams = parsePaginationParams(request);
  console.log('📄 分页参数:', paginationParams);

  // 解析筛选参数
  const queryParams = parseQueryParams(request);
  const filters: TicketTypeFilters = {
    ticket_type_name: queryParams.ticket_type_name,
    category: queryParams.category
  };
  console.log('🔍 筛选条件:', filters);

  // 查询数据
  const result = await ticketTypeDAO.findWithFilters(paginationParams, filters);

  console.log(`✅ 成功获取票种列表，共${result.total}条记录`);
  return createSuccessResponse(result, '获取票种列表成功');
});

/**
 * POST /api/ticket-types
 * 创建新票种
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  console.log('📥 POST /api/ticket-types - 创建新票种');

  // 解析请求体
  const body = await parseRequestBody<CreateTicketTypeInput>(request);
  console.log('📝 创建数据:', body);

  // 验证必需字段
  const { isValid, missingFields } = validateRequiredFields(body, ['ticket_type_name']);
  if (!isValid) {
    console.log('❌ 缺少必需字段:', missingFields);
    return createValidationErrorResponse(
      `缺少必需字段: ${missingFields.join(', ')}`,
      { missingFields }
    );
  }

  // 验证字段长度
  if (!validateStringLength(body.ticket_type_name, 1, 100)) {
    return createValidationErrorResponse('票种名称长度必须在1-100个字符之间');
  }

  if (body.category && !validateStringLength(body.category, 0, 50)) {
    return createValidationErrorResponse('票种分类长度不能超过50个字符');
  }

  // 检查票种名称是否已存在
  const existingTicketType = await ticketTypeDAO.findByName(body.ticket_type_name);
  if (existingTicketType) {
    return createValidationErrorResponse('票种名称已存在');
  }

  // 创建票种
  const ticketTypeId = await ticketTypeDAO.create(body);

  // 获取创建的票种详情
  const newTicketType = await ticketTypeDAO.findById(ticketTypeId);

  console.log(`✅ 成功创建票种，ID: ${ticketTypeId}`);
  return createSuccessResponse(newTicketType, '创建票种成功');
});

/**
 * 处理不支持的HTTP方法
 */
export async function PUT(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'POST']);
}

export async function DELETE(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'POST']);
}

export async function PATCH(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'POST']);
}
