/**
 * 票种表单组件
 * 
 * 功能说明：
 * 1. 支持创建、编辑、查看三种模式
 * 2. 表单验证和错误处理
 * 3. 响应式设计
 * 4. 加载状态管理
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import { TicketType, CreateTicketTypeInput, UpdateTicketTypeInput } from '@/types';

// ============================================================================
// 表单验证模式
// ============================================================================

const ticketTypeSchema = z.object({
  ticket_type_name: z.string()
    .min(1, '票种名称不能为空')
    .max(100, '票种名称不能超过100个字符'),
  category: z.string()
    .max(50, '分类不能超过50个字符')
    .optional()
    .or(z.literal(''))
});

type TicketTypeFormData = z.infer<typeof ticketTypeSchema>;

// ============================================================================
// 组件属性接口
// ============================================================================

interface TicketTypeFormProps {
  mode: 'create' | 'edit' | 'view';
  initialData?: TicketType;
  onSubmit: (data: CreateTicketTypeInput | UpdateTicketTypeInput) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  className?: string;
}

// ============================================================================
// 票种表单组件实现
// ============================================================================

export const TicketTypeForm: React.FC<TicketTypeFormProps> = ({
  mode,
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  className
}) => {
  const [submitError, setSubmitError] = useState<string>('');
  const [submitSuccess, setSubmitSuccess] = useState<string>('');

  const isReadonly = mode === 'view';
  const isEdit = mode === 'edit';
  const isCreate = mode === 'create';

  // 表单配置
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset
  } = useForm<TicketTypeFormData>({
    resolver: zodResolver(ticketTypeSchema),
    defaultValues: {
      ticket_type_name: initialData?.ticket_type_name || '',
      category: initialData?.category || ''
    }
  });

  // 重置表单数据
  useEffect(() => {
    if (initialData) {
      reset({
        ticket_type_name: initialData.ticket_type_name,
        category: initialData.category || ''
      });
    }
  }, [initialData, reset]);

  // 表单提交处理
  const handleFormSubmit = async (data: TicketTypeFormData) => {
    try {
      setSubmitError('');
      setSubmitSuccess('');

      // 清理空字符串字段
      const cleanedData = {
        ...data,
        category: data.category?.trim() || null
      };

      await onSubmit(cleanedData);

      if (isCreate) {
        setSubmitSuccess('票种创建成功！');
        reset(); // 重置表单
      } else if (isEdit) {
        setSubmitSuccess('票种更新成功！');
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : '操作失败，请重试');
    }
  };

  // 获取表单标题
  const getTitle = () => {
    switch (mode) {
      case 'create':
        return '创建票种';
      case 'edit':
        return '编辑票种';
      case 'view':
        return '查看票种';
      default:
        return '票种信息';
    }
  };

  // 获取表单描述
  const getDescription = () => {
    switch (mode) {
      case 'create':
        return '填写票种基本信息，创建新的票种记录';
      case 'edit':
        return '修改票种信息，更新现有记录';
      case 'view':
        return '查看票种的详细信息';
      default:
        return '';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{getTitle()}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* 错误提示 */}
          {submitError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* 成功提示 */}
          {submitSuccess && (
            <Alert className="border-green-200 bg-green-50 text-green-800">
              <CheckCircle2 className="h-4 w-4" />
              <AlertDescription>{submitSuccess}</AlertDescription>
            </Alert>
          )}

          <div className="grid gap-6">
          {/* 票种名称 */}
          <div className="space-y-2">
            <Label htmlFor="ticket_type_name" className="text-sm font-medium">
              票种名称 <span className="text-red-500">*</span>
            </Label>
            <Input
              id="ticket_type_name"
              {...register('ticket_type_name')}
              placeholder="请输入票种名称"
              disabled={isReadonly || loading}
              className={errors.ticket_type_name ? 'border-red-500' : ''}
            />
            {errors.ticket_type_name && (
              <p className="text-sm text-red-500">{errors.ticket_type_name.message}</p>
            )}
          </div>

          {/* 分类 */}
          <div className="space-y-2">
            <Label htmlFor="category" className="text-sm font-medium">
              分类
            </Label>
            <Input
              id="category"
              {...register('category')}
              placeholder="请输入票种分类（可选）"
              disabled={isReadonly || loading}
              className={errors.category ? 'border-red-500' : ''}
            />
            {errors.category && (
              <p className="text-sm text-red-500">{errors.category.message}</p>
            )}
            <p className="text-xs text-gray-500">
              例如：成人票、儿童票、学生票、老人票等
            </p>
          </div>

          {/* 只读模式显示额外信息 */}
          {isReadonly && initialData && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-600">票种ID</Label>
                <div className="text-sm text-gray-900">{initialData.ticket_type_id}</div>
              </div>
            </div>
          )}

          {/* 表单按钮 */}
          <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 pt-6">
            {!isReadonly && (
              <Button
                type="submit"
                disabled={loading || isSubmitting}
                className="min-w-[100px]"
              >
                {(loading || isSubmitting) && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {isCreate ? '创建票种' : '保存更改'}
              </Button>
            )}
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading || isSubmitting}
              className="mb-2 sm:mb-0"
            >
              {isReadonly ? '关闭' : '取消'}
            </Button>
          </div>
        </div>
        </form>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// 导出组件
// ============================================================================

export default TicketTypeForm;
