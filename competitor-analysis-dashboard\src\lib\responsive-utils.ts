/**
 * 响应式设计工具函数
 * 
 * 功能说明：
 * 1. 提供屏幕尺寸检测工具
 * 2. 响应式断点管理
 * 3. 移动端特性检测
 * 4. 触摸设备检测
 */

'use client';

import { useState, useEffect } from 'react';

// ============================================================================
// 响应式断点定义
// ============================================================================

export const breakpoints = {
  xs: 0,      // 超小屏幕
  sm: 640,    // 小屏幕
  md: 768,    // 中等屏幕
  lg: 1024,   // 大屏幕
  xl: 1280,   // 超大屏幕
  '2xl': 1536 // 2倍超大屏幕
} as const;

export type Breakpoint = keyof typeof breakpoints;

// ============================================================================
// 屏幕尺寸检测Hook
// ============================================================================

export interface ScreenSize {
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  currentBreakpoint: Breakpoint;
}

export const useScreenSize = (): ScreenSize => {
  const [screenSize, setScreenSize] = useState<ScreenSize>({
    width: 0,
    height: 0,
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    currentBreakpoint: 'xs'
  });

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      // 确定当前断点
      let currentBreakpoint: Breakpoint = 'xs';
      if (width >= breakpoints['2xl']) currentBreakpoint = '2xl';
      else if (width >= breakpoints.xl) currentBreakpoint = 'xl';
      else if (width >= breakpoints.lg) currentBreakpoint = 'lg';
      else if (width >= breakpoints.md) currentBreakpoint = 'md';
      else if (width >= breakpoints.sm) currentBreakpoint = 'sm';

      setScreenSize({
        width,
        height,
        isMobile: width < breakpoints.md,
        isTablet: width >= breakpoints.md && width < breakpoints.lg,
        isDesktop: width >= breakpoints.lg,
        currentBreakpoint
      });
    };

    // 初始化
    updateScreenSize();

    // 监听窗口大小变化
    window.addEventListener('resize', updateScreenSize);
    
    return () => {
      window.removeEventListener('resize', updateScreenSize);
    };
  }, []);

  return screenSize;
};

// ============================================================================
// 断点匹配Hook
// ============================================================================

export const useBreakpoint = (breakpoint: Breakpoint): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const checkBreakpoint = () => {
      setMatches(window.innerWidth >= breakpoints[breakpoint]);
    };

    // 初始检查
    checkBreakpoint();

    // 监听窗口大小变化
    window.addEventListener('resize', checkBreakpoint);
    
    return () => {
      window.removeEventListener('resize', checkBreakpoint);
    };
  }, [breakpoint]);

  return matches;
};

// ============================================================================
// 移动端检测Hook
// ============================================================================

export const useIsMobile = (): boolean => {
  return useBreakpoint('md') === false;
};

export const useIsTablet = (): boolean => {
  const isMd = useBreakpoint('md');
  const isLg = useBreakpoint('lg');
  return isMd && !isLg;
};

export const useIsDesktop = (): boolean => {
  return useBreakpoint('lg');
};

// ============================================================================
// 触摸设备检测
// ============================================================================

export const useIsTouchDevice = (): boolean => {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    const checkTouchDevice = () => {
      setIsTouchDevice(
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        // @ts-ignore
        navigator.msMaxTouchPoints > 0
      );
    };

    checkTouchDevice();
  }, []);

  return isTouchDevice;
};

// ============================================================================
// 设备方向检测
// ============================================================================

export type Orientation = 'portrait' | 'landscape';

export const useOrientation = (): Orientation => {
  const [orientation, setOrientation] = useState<Orientation>('portrait');

  useEffect(() => {
    const updateOrientation = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };

    // 初始化
    updateOrientation();

    // 监听方向变化
    window.addEventListener('resize', updateOrientation);
    window.addEventListener('orientationchange', updateOrientation);
    
    return () => {
      window.removeEventListener('resize', updateOrientation);
      window.removeEventListener('orientationchange', updateOrientation);
    };
  }, []);

  return orientation;
};

// ============================================================================
// 响应式值Hook
// ============================================================================

export interface ResponsiveValue<T> {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
}

export const useResponsiveValue = <T>(values: ResponsiveValue<T>): T | undefined => {
  const { currentBreakpoint } = useScreenSize();

  // 按优先级查找值
  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);

  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i];
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }

  return undefined;
};

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 检查当前是否为移动端
 */
export const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < breakpoints.md;
};

/**
 * 检查当前是否为平板设备
 */
export const isTabletDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  const width = window.innerWidth;
  return width >= breakpoints.md && width < breakpoints.lg;
};

/**
 * 检查当前是否为桌面设备
 */
export const isDesktopDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= breakpoints.lg;
};

/**
 * 获取当前断点
 */
export const getCurrentBreakpoint = (): Breakpoint => {
  if (typeof window === 'undefined') return 'xs';
  
  const width = window.innerWidth;
  if (width >= breakpoints['2xl']) return '2xl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'xs';
};

/**
 * 生成响应式类名
 */
export const getResponsiveClasses = (
  baseClass: string,
  responsiveClasses: Partial<Record<Breakpoint, string>>
): string => {
  const classes = [baseClass];
  
  Object.entries(responsiveClasses).forEach(([breakpoint, className]) => {
    if (className) {
      const prefix = breakpoint === 'xs' ? '' : `${breakpoint}:`;
      classes.push(`${prefix}${className}`);
    }
  });
  
  return classes.join(' ');
};

// ============================================================================
// 导出所有工具
// ============================================================================

export {
  useScreenSize,
  useBreakpoint,
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  useIsTouchDevice,
  useOrientation,
  useResponsiveValue
};
