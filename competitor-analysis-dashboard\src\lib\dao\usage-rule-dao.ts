/**
 * 使用规则数据访问对象(DAO)类
 * 
 * 功能说明：
 * 1. 实现使用规则表的CRUD操作
 * 2. 提供规则查询和管理功能
 * 3. 支持规则的启用/禁用状态管理
 */

import { BaseDAO } from './base-dao';
import { executeQuery } from '../database';
import {
  UsageRule,
  CreateUsageRuleInput,
  UpdateUsageRuleInput,
  PaginationParams,
  PaginatedResult
} from '@/types';

/**
 * 使用规则DAO类
 * 继承基础DAO类，实现使用规则特有的数据操作
 */
export class UsageRuleDAO extends BaseDAO<UsageRule, CreateUsageRuleInput, UpdateUsageRuleInput> {
  constructor() {
    super('UsageRules', 'rule_id');
  }

  /**
   * 获取所有启用的使用规则
   * @returns 启用的使用规则列表
   */
  async findActive(): Promise<UsageRule[]> {
    try {
      console.log('🔍 查询所有启用的使用规则');
      
      const query = `
        SELECT rule_id, rule_description, is_active
        FROM UsageRules 
        WHERE is_active = TRUE
        ORDER BY rule_id ASC
      `;
      
      const results = await executeQuery<UsageRule>(query);
      
      console.log(`✅ 成功获取${results.length}条启用的使用规则`);
      return results;
    } catch (error) {
      console.error('❌ 查询启用的使用规则失败:', error);
      throw new Error(`查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据规则描述查找规则
   * @param description 规则描述
   * @returns 使用规则或null
   */
  async findByDescription(description: string): Promise<UsageRule | null> {
    try {
      console.log(`🔍 根据描述查找使用规则: ${description}`);
      
      const query = `
        SELECT rule_id, rule_description, is_active
        FROM UsageRules 
        WHERE rule_description = ?
        LIMIT 1
      `;
      
      const results = await executeQuery<UsageRule>(query, [description]);
      
      if (results.length === 0) {
        console.log('📭 未找到匹配的使用规则');
        return null;
      }
      
      console.log(`✅ 成功找到使用规则，ID: ${results[0].rule_id}`);
      return results[0];
    } catch (error) {
      console.error('❌ 根据描述查找使用规则失败:', error);
      throw new Error(`查找失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取规则描述到ID的映射表
   * @returns 规则描述映射表
   */
  async getDescriptionMap(): Promise<Map<string, number>> {
    try {
      console.log('🔍 获取使用规则描述映射表');
      
      const query = `
        SELECT rule_id, rule_description
        FROM UsageRules 
        WHERE is_active = TRUE
        ORDER BY rule_id ASC
      `;
      
      const results = await executeQuery<{ rule_id: number; rule_description: string }>(query);
      
      const map = new Map<string, number>();
      results.forEach(rule => {
        map.set(rule.rule_description, rule.rule_id);
      });
      
      console.log(`✅ 成功获取${map.size}条规则映射`);
      return map;
    } catch (error) {
      console.error('❌ 获取规则描述映射表失败:', error);
      throw new Error(`获取映射表失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 检查规则描述是否已存在
   * @param description 规则描述
   * @param excludeId 排除的规则ID（用于更新时检查）
   * @returns 是否已存在
   */
  async isDescriptionExists(description: string, excludeId?: number): Promise<boolean> {
    try {
      let query = 'SELECT 1 FROM UsageRules WHERE rule_description = ?';
      const params: any[] = [description];

      if (excludeId) {
        query += ' AND rule_id != ?';
        params.push(excludeId);
      }

      const results = await executeQuery(query, params);
      return results.length > 0;
    } catch (error) {
      console.error('❌ 检查规则描述是否存在失败:', error);
      throw new Error(`检查失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 创建或获取使用规则
   * 如果规则描述已存在则返回现有规则，否则创建新规则
   * @param description 规则描述
   * @returns 规则ID
   */
  async createOrGet(description: string): Promise<number> {
    try {
      // 先尝试查找现有规则
      const existing = await this.findByDescription(description);
      if (existing) {
        console.log(`📋 使用现有规则，ID: ${existing.rule_id}`);
        return existing.rule_id;
      }

      // 创建新规则
      console.log(`➕ 创建新使用规则: ${description}`);
      const ruleId = await this.create({
        rule_description: description,
        is_active: true
      });

      console.log(`✅ 成功创建新规则，ID: ${ruleId}`);
      return ruleId;
    } catch (error) {
      console.error('❌ 创建或获取使用规则失败:', error);
      throw new Error(`操作失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 切换规则的启用状态
   * @param ruleId 规则ID
   * @returns 更新后的规则
   */
  async toggleActive(ruleId: number): Promise<UsageRule> {
    try {
      console.log(`🔄 切换规则启用状态，ID: ${ruleId}`);
      
      // 先获取当前状态
      const current = await this.findById(ruleId);
      if (!current) {
        throw new Error('规则不存在');
      }

      // 切换状态
      const newStatus = !current.is_active;
      await this.update(ruleId, { is_active: newStatus });

      // 返回更新后的规则
      const updated = await this.findById(ruleId);
      if (!updated) {
        throw new Error('更新后无法获取规则信息');
      }

      console.log(`✅ 成功切换规则状态为: ${newStatus ? '启用' : '禁用'}`);
      return updated;
    } catch (error) {
      console.error('❌ 切换规则启用状态失败:', error);
      throw new Error(`切换状态失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

// 导出单例实例
export const usageRuleDAO = new UsageRuleDAO();
