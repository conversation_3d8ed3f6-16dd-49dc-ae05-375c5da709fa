/**
 * 竞品API路由
 * 
 * 功能说明：
 * 1. 处理竞品的CRUD操作
 * 2. 支持分页查询和筛选
 * 3. 提供数据验证和错误处理
 * 4. 实现RESTful API规范
 */

import { NextRequest } from 'next/server';
import { competitorDAO } from '@/lib/dao';
import {
  createSuccessResponse,
  createErrorResponse,
  createValidationErrorResponse,
  parsePaginationParams,
  parseQueryParams,
  parseRequestBody,
  validateRequiredFields,
  validateStringLength,
  withErrorHandling,
  validateHttpMethod,
  createMethodNotAllowedResponse
} from '@/lib/api-utils';
import { CreateCompetitorInput, CompetitorFilters } from '@/types';

/**
 * GET /api/competitors
 * 获取竞品列表（支持分页和筛选）
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('📥 GET /api/competitors - 获取竞品列表');

  // 解析分页参数
  const paginationParams = parsePaginationParams(request);
  console.log('📄 分页参数:', paginationParams);

  // 解析筛选参数
  const queryParams = parseQueryParams(request);
  const filters: CompetitorFilters = {
    competitor_name: queryParams.competitor_name,
    city: queryParams.city,
    park_type: queryParams.park_type,
    is_active: queryParams.is_active
  };
  console.log('🔍 筛选条件:', filters);

  // 查询数据
  const result = await competitorDAO.findWithFilters(paginationParams, filters);

  console.log(`✅ 成功获取竞品列表，共${result.total}条记录`);
  return createSuccessResponse(result, '获取竞品列表成功');
});

/**
 * POST /api/competitors
 * 创建新竞品
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  console.log('📥 POST /api/competitors - 创建新竞品');

  // 解析请求体
  const body = await parseRequestBody<CreateCompetitorInput>(request);
  console.log('📝 创建数据:', body);

  // 验证必需字段
  const { isValid, missingFields } = validateRequiredFields(body, ['competitor_name']);
  if (!isValid) {
    console.log('❌ 缺少必需字段:', missingFields);
    return createValidationErrorResponse(
      `缺少必需字段: ${missingFields.join(', ')}`,
      { missingFields }
    );
  }

  // 验证字段长度
  if (!validateStringLength(body.competitor_name, 1, 100)) {
    return createValidationErrorResponse('竞品名称长度必须在1-100个字符之间');
  }

  if (body.city && !validateStringLength(body.city, 0, 50)) {
    return createValidationErrorResponse('城市名称长度不能超过50个字符');
  }

  if (body.park_type && !validateStringLength(body.park_type, 0, 50)) {
    return createValidationErrorResponse('园区类型长度不能超过50个字符');
  }

  // 检查竞品名称是否已存在
  const existingCompetitor = await competitorDAO.findByName(body.competitor_name);
  if (existingCompetitor) {
    return createValidationErrorResponse('竞品名称已存在');
  }

  // 创建竞品
  const competitorId = await competitorDAO.create(body);

  // 获取创建的竞品详情
  const newCompetitor = await competitorDAO.findById(competitorId);

  console.log(`✅ 成功创建竞品，ID: ${competitorId}`);
  return createSuccessResponse(newCompetitor, '创建竞品成功');
});

/**
 * 处理不支持的HTTP方法
 */
export async function PUT(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'POST']);
}

export async function DELETE(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'POST']);
}

export async function PATCH(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'POST']);
}
