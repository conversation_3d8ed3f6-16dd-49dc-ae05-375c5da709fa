/**
 * 使用规则API路由
 * 
 * 功能说明：
 * 1. 提供使用规则的CRUD操作
 * 2. 支持查询所有启用的规则
 * 3. 支持创建新规则和更新现有规则
 */

import { NextRequest, NextResponse } from 'next/server';
import { usageRuleDAO } from '@/lib/dao';
import { CreateUsageRuleInput, UpdateUsageRuleInput } from '@/types';
import { 
  withErrorHandling, 
  parseRequestBody, 
  validateRequiredFields,
  createSuccessResponse,
  createValidationErrorResponse,
  validateStringLength
} from '@/lib/api-utils';

// ============================================================================
// GET /api/usage-rules
// 获取使用规则列表
// ============================================================================

export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('📥 GET /api/usage-rules - 获取使用规则列表');

  try {
    // 获取所有启用的使用规则
    const usageRules = await usageRuleDAO.findActive();
    
    console.log(`✅ 成功获取${usageRules.length}条使用规则`);
    return createSuccessResponse(usageRules, '获取使用规则列表成功');
  } catch (error) {
    console.error('❌ 获取使用规则列表失败:', error);
    throw error;
  }
});

// ============================================================================
// POST /api/usage-rules
// 创建新使用规则
// ============================================================================

export const POST = withErrorHandling(async (request: NextRequest) => {
  console.log('📥 POST /api/usage-rules - 创建新使用规则');

  // 解析请求体
  const body = await parseRequestBody<CreateUsageRuleInput>(request);
  console.log('📝 创建数据:', body);

  // 验证必需字段
  const { isValid, missingFields } = validateRequiredFields(body, ['rule_description']);
  if (!isValid) {
    console.log('❌ 缺少必需字段:', missingFields);
    return createValidationErrorResponse(
      `缺少必需字段: ${missingFields.join(', ')}`,
      { missingFields }
    );
  }

  // 验证字段格式和长度
  if (!validateStringLength(body.rule_description, 1, 255)) {
    return createValidationErrorResponse('规则描述长度必须在1-255个字符之间');
  }

  // 检查规则描述是否已存在
  const exists = await usageRuleDAO.isDescriptionExists(body.rule_description);
  if (exists) {
    return createValidationErrorResponse('该规则描述已存在');
  }

  // 创建使用规则
  const ruleId = await usageRuleDAO.create(body);

  // 获取创建的使用规则详情
  const newRule = await usageRuleDAO.findById(ruleId);

  console.log(`✅ 成功创建使用规则，ID: ${ruleId}`);
  return createSuccessResponse(newRule, '创建使用规则成功');
});


