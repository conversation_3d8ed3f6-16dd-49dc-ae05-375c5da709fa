/**
 * 移动端导航组件
 * 
 * 功能说明：
 * 1. 专为移动端设计的导航菜单
 * 2. 底部标签栏导航
 * 3. 手势友好的交互设计
 * 4. 适配不同屏幕尺寸
 */

'use client';

import React from 'react';
import { Home, Users, Ticket, Database, BarChart3, Settings, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';

// ============================================================================
// 导航项配置
// ============================================================================

interface NavItem {
  key: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  badge?: number;
}

const navItems: NavItem[] = [
  {
    key: 'dashboard',
    label: '首页',
    icon: Home,
    href: '/'
  },
  {
    key: 'competitors',
    label: '竞品',
    icon: Users,
    href: '/competitors'
  },
  {
    key: 'ticket-types',
    label: '票种',
    icon: Ticket,
    href: '/ticket-types'
  },
  {
    key: 'promotions',
    label: '促销',
    icon: Database,
    href: '/promotions'
  },
  {
    key: 'usage-rules',
    label: '规则',
    icon: FileText,
    href: '/usage-rules'
  },
  {
    key: 'analytics',
    label: '分析',
    icon: BarChart3,
    href: '/analytics'
  }
];

// ============================================================================
// 移动端底部导航组件
// ============================================================================

export interface MobileBottomNavProps {
  currentPath?: string;
  className?: string;
}

export const MobileBottomNav: React.FC<MobileBottomNavProps> = ({
  currentPath = '/',
  className
}) => {
  return (
    <nav className={cn(
      'fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 md:hidden',
      'safe-area-pb', // 适配iPhone底部安全区域
      className
    )}>
      <div className="flex items-center justify-around px-2 py-1">
        {navItems.map((item) => {
          const isActive = currentPath === item.href || 
            (item.href !== '/' && currentPath.startsWith(item.href));
          const Icon = item.icon;

          return (
            <a
              key={item.key}
              href={item.href}
              className={cn(
                'flex flex-col items-center justify-center min-w-0 flex-1 px-1 py-2 text-xs transition-colors',
                'active:bg-gray-100 rounded-lg',
                isActive
                  ? 'text-blue-600'
                  : 'text-gray-600 hover:text-gray-900'
              )}
            >
              <div className="relative">
                <Icon className={cn(
                  'h-5 w-5 mb-1',
                  isActive ? 'text-blue-600' : 'text-gray-400'
                )} />
                {item.badge && item.badge > 0 && (
                  <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {item.badge > 99 ? '99+' : item.badge}
                  </span>
                )}
              </div>
              <span className={cn(
                'truncate max-w-full',
                isActive ? 'font-medium' : 'font-normal'
              )}>
                {item.label}
              </span>
            </a>
          );
        })}
      </div>
    </nav>
  );
};

// ============================================================================
// 移动端侧边抽屉导航组件
// ============================================================================

export interface MobileDrawerNavProps {
  open: boolean;
  onClose: () => void;
  currentPath?: string;
  className?: string;
}

export const MobileDrawerNav: React.FC<MobileDrawerNavProps> = ({
  open,
  onClose,
  currentPath = '/',
  className
}) => {
  return (
    <>
      {/* 遮罩层 */}
      {open && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden"
          onClick={onClose}
        />
      )}

      {/* 抽屉内容 */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:hidden',
          open ? 'translate-x-0' : '-translate-x-full',
          className
        )}
      >
        {/* 抽屉头部 */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            竞品分析系统
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
          >
            <span className="sr-only">关闭菜单</span>
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {navItems.map((item) => {
            const isActive = currentPath === item.href || 
              (item.href !== '/' && currentPath.startsWith(item.href));
            const Icon = item.icon;

            return (
              <a
                key={item.key}
                href={item.href}
                onClick={onClose}
                className={cn(
                  'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',
                  isActive
                    ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                )}
              >
                <Icon className="mr-3 h-5 w-5" />
                <span className="flex-1">{item.label}</span>
                {item.badge && item.badge > 0 && (
                  <span className="ml-2 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {item.badge > 99 ? '99+' : item.badge}
                  </span>
                )}
              </a>
            );
          })}
        </nav>

        {/* 抽屉底部 */}
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            版本 1.0.0
          </div>
        </div>
      </div>
    </>
  );
};

// ============================================================================
// 移动端顶部导航栏组件
// ============================================================================

export interface MobileTopNavProps {
  title?: string;
  onMenuClick?: () => void;
  actions?: React.ReactNode;
  className?: string;
}

export const MobileTopNav: React.FC<MobileTopNavProps> = ({
  title = '竞品分析系统',
  onMenuClick,
  actions,
  className
}) => {
  return (
    <header className={cn(
      'sticky top-0 z-30 bg-white border-b border-gray-200 md:hidden',
      className
    )}>
      <div className="flex items-center justify-between h-14 px-4">
        {/* 左侧菜单按钮 */}
        <button
          onClick={onMenuClick}
          className="p-2 -ml-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
        >
          <span className="sr-only">打开菜单</span>
          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        {/* 中间标题 */}
        <h1 className="text-lg font-semibold text-gray-900 truncate flex-1 mx-4 text-center">
          {title}
        </h1>

        {/* 右侧操作按钮 */}
        <div className="flex items-center space-x-2">
          {actions}
        </div>
      </div>
    </header>
  );
};

// ============================================================================
// 移动端浮动操作按钮组件
// ============================================================================

export interface MobileFABProps {
  onClick?: () => void;
  icon?: React.ComponentType<{ className?: string }>;
  label?: string;
  className?: string;
}

export const MobileFAB: React.FC<MobileFABProps> = ({
  onClick,
  icon: Icon = Database,
  label = '添加',
  className
}) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        'fixed bottom-20 right-4 z-40 md:hidden',
        'h-14 w-14 bg-blue-600 text-white rounded-full shadow-lg',
        'flex items-center justify-center',
        'hover:bg-blue-700 active:bg-blue-800',
        'transition-colors duration-200',
        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
        className
      )}
    >
      <Icon className="h-6 w-6" />
      <span className="sr-only">{label}</span>
    </button>
  );
};

// ============================================================================
// 导出所有组件
// ============================================================================

export {
  MobileBottomNav as default,
  MobileDrawerNav,
  MobileTopNav,
  MobileFAB
};
