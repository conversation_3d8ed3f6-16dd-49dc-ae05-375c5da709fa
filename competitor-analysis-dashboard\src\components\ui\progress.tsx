/**
 * Progress进度条组件
 * 
 * 功能说明：
 * 1. 显示操作进度的可视化组件
 * 2. 支持自定义样式和颜色
 * 3. 响应式设计，适配不同屏幕尺寸
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';

// ============================================================================
// 组件接口定义
// ============================================================================

interface ProgressProps {
  value: number;           // 进度值（0-100）
  max?: number;           // 最大值，默认100
  className?: string;     // 自定义CSS类名
  indicatorClassName?: string; // 进度指示器CSS类名
  showValue?: boolean;    // 是否显示数值
  size?: 'sm' | 'md' | 'lg'; // 尺寸大小
  variant?: 'default' | 'success' | 'warning' | 'error'; // 样式变体
}

// ============================================================================
// Progress组件
// ============================================================================

export function Progress({
  value,
  max = 100,
  className,
  indicatorClassName,
  showValue = false,
  size = 'md',
  variant = 'default'
}: ProgressProps) {
  // 计算进度百分比
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  // 尺寸样式映射
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  // 变体样式映射
  const variantClasses = {
    default: 'bg-blue-600',
    success: 'bg-green-600',
    warning: 'bg-yellow-600',
    error: 'bg-red-600'
  };

  return (
    <div className={cn('w-full', className)}>
      {/* 进度条容器 */}
      <div
        className={cn(
          'relative w-full overflow-hidden rounded-full bg-gray-200',
          sizeClasses[size]
        )}
      >
        {/* 进度指示器 */}
        <div
          className={cn(
            'h-full transition-all duration-300 ease-in-out',
            variantClasses[variant],
            indicatorClassName
          )}
          style={{
            width: `${percentage}%`
          }}
        />
      </div>

      {/* 数值显示 */}
      {showValue && (
        <div className="mt-1 text-right text-xs text-gray-600">
          {Math.round(percentage)}%
        </div>
      )}
    </div>
  );
}

// ============================================================================
// 带标签的Progress组件
// ============================================================================

interface LabeledProgressProps extends ProgressProps {
  label?: string;         // 标签文本
  description?: string;   // 描述文本
}

export function LabeledProgress({
  label,
  description,
  value,
  max = 100,
  className,
  ...props
}: LabeledProgressProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  return (
    <div className={cn('space-y-2', className)}>
      {/* 标签和数值 */}
      {(label || props.showValue) && (
        <div className="flex items-center justify-between text-sm">
          {label && <span className="font-medium text-gray-700">{label}</span>}
          {props.showValue && (
            <span className="text-gray-600">{Math.round(percentage)}%</span>
          )}
        </div>
      )}

      {/* 进度条 */}
      <Progress value={value} max={max} {...props} showValue={false} />

      {/* 描述文本 */}
      {description && (
        <p className="text-xs text-gray-500">{description}</p>
      )}
    </div>
  );
}

// ============================================================================
// 圆形Progress组件
// ============================================================================

interface CircularProgressProps {
  value: number;          // 进度值（0-100）
  max?: number;          // 最大值，默认100
  size?: number;         // 圆形大小，默认40
  strokeWidth?: number;  // 线条宽度，默认4
  className?: string;    // 自定义CSS类名
  showValue?: boolean;   // 是否显示数值
  variant?: 'default' | 'success' | 'warning' | 'error'; // 样式变体
}

export function CircularProgress({
  value,
  max = 100,
  size = 40,
  strokeWidth = 4,
  className,
  showValue = true,
  variant = 'default'
}: CircularProgressProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  // 变体颜色映射
  const variantColors = {
    default: '#3b82f6', // blue-500
    success: '#10b981', // emerald-500
    warning: '#f59e0b', // amber-500
    error: '#ef4444'    // red-500
  };

  return (
    <div className={cn('relative inline-flex items-center justify-center', className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* 背景圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#e5e7eb"
          strokeWidth={strokeWidth}
          fill="none"
        />
        {/* 进度圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={variantColors[variant]}
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="transition-all duration-300 ease-in-out"
        />
      </svg>
      
      {/* 中心数值 */}
      {showValue && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-medium text-gray-700">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
    </div>
  );
}
