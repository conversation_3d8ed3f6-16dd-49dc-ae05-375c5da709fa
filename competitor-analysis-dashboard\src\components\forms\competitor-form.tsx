/**
 * 竞品表单组件
 * 
 * 功能说明：
 * 1. 提供竞品的创建和编辑表单
 * 2. 实现表单验证和错误处理
 * 3. 支持不同的表单模式（创建/编辑/查看）
 * 4. 响应式设计
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { LoadingButton } from '@/components/ui/loading';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Competitor, CreateCompetitorInput, UpdateCompetitorInput } from '@/types';

// ============================================================================
// 表单验证模式
// ============================================================================

const competitorSchema = z.object({
  competitor_name: z
    .string()
    .min(1, '竞品名称不能为空')
    .max(100, '竞品名称不能超过100个字符'),
  city: z
    .string()
    .max(50, '城市名称不能超过50个字符')
    .optional()
    .or(z.literal('')),
  park_type: z
    .string()
    .max(50, '园区类型不能超过50个字符')
    .optional()
    .or(z.literal('')),
  is_active: z.boolean().default(true)
});

type CompetitorFormData = z.infer<typeof competitorSchema>;

// ============================================================================
// 表单组件接口
// ============================================================================

export interface CompetitorFormProps {
  mode: 'create' | 'edit' | 'view';
  initialData?: Competitor;
  onSubmit: (data: CreateCompetitorInput | UpdateCompetitorInput) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  className?: string;
}

// ============================================================================
// 竞品表单组件实现
// ============================================================================

export const CompetitorForm: React.FC<CompetitorFormProps> = ({
  mode,
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  className
}) => {
  const [submitError, setSubmitError] = useState<string>('');
  const [submitSuccess, setSubmitSuccess] = useState<string>('');

  const isReadonly = mode === 'view';
  const isEdit = mode === 'edit';
  const isCreate = mode === 'create';

  // 表单配置
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
    reset
  } = useForm<CompetitorFormData>({
    resolver: zodResolver(competitorSchema),
    defaultValues: {
      competitor_name: initialData?.competitor_name || '',
      city: initialData?.city || '',
      park_type: initialData?.park_type || '',
      is_active: initialData?.is_active ?? true
    }
  });

  // 监听is_active字段变化
  const isActive = watch('is_active');

  // 重置表单数据
  useEffect(() => {
    if (initialData) {
      reset({
        competitor_name: initialData.competitor_name,
        city: initialData.city || '',
        park_type: initialData.park_type || '',
        is_active: initialData.is_active
      });
    }
  }, [initialData, reset]);

  // 表单提交处理
  const handleFormSubmit = async (data: CompetitorFormData) => {
    try {
      setSubmitError('');
      setSubmitSuccess('');

      // 清理空字符串字段
      const cleanData = {
        ...data,
        city: data.city || undefined,
        park_type: data.park_type || undefined
      };

      await onSubmit(cleanData);

      if (isCreate) {
        setSubmitSuccess('竞品创建成功！');
        reset(); // 创建成功后重置表单
      } else {
        setSubmitSuccess('竞品更新成功！');
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : '操作失败');
    }
  };

  // 获取表单标题
  const getFormTitle = () => {
    switch (mode) {
      case 'create':
        return '创建竞品';
      case 'edit':
        return '编辑竞品';
      case 'view':
        return '查看竞品';
      default:
        return '竞品表单';
    }
  };

  // 获取提交按钮文本
  const getSubmitButtonText = () => {
    if (isSubmitting || loading) {
      return isCreate ? '创建中...' : '更新中...';
    }
    return isCreate ? '创建竞品' : '更新竞品';
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{getFormTitle()}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* 竞品名称 */}
          <div className="space-y-2">
            <Label htmlFor="competitor_name" className="text-sm font-medium">
              竞品名称 <span className="text-red-500">*</span>
            </Label>
            <Input
              id="competitor_name"
              {...register('competitor_name')}
              placeholder="请输入竞品名称"
              disabled={isReadonly}
              className={errors.competitor_name ? 'border-red-500' : ''}
            />
            {errors.competitor_name && (
              <p className="text-sm text-red-500">
                {errors.competitor_name.message}
              </p>
            )}
          </div>

          {/* 所在城市 */}
          <div className="space-y-2">
            <Label htmlFor="city" className="text-sm font-medium">
              所在城市
            </Label>
            <Input
              id="city"
              {...register('city')}
              placeholder="请输入所在城市"
              disabled={isReadonly}
              className={errors.city ? 'border-red-500' : ''}
            />
            {errors.city && (
              <p className="text-sm text-red-500">
                {errors.city.message}
              </p>
            )}
          </div>

          {/* 园区类型 */}
          <div className="space-y-2">
            <Label htmlFor="park_type" className="text-sm font-medium">
              园区类型
            </Label>
            <Input
              id="park_type"
              {...register('park_type')}
              placeholder="请输入园区类型"
              disabled={isReadonly}
              className={errors.park_type ? 'border-red-500' : ''}
            />
            {errors.park_type && (
              <p className="text-sm text-red-500">
                {errors.park_type.message}
              </p>
            )}
          </div>

          {/* 活跃状态 */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={isActive}
              onCheckedChange={(checked) => setValue('is_active', checked)}
              disabled={isReadonly}
            />
            <Label htmlFor="is_active" className="text-sm font-medium">
              活跃状态
            </Label>
          </div>

          {/* 错误提示 */}
          {submitError && (
            <Alert variant="destructive">
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* 成功提示 */}
          {submitSuccess && (
            <Alert>
              <AlertDescription>{submitSuccess}</AlertDescription>
            </Alert>
          )}

          {/* 操作按钮 */}
          {!isReadonly && (
            <div className="flex gap-4 pt-4">
              <LoadingButton
                type="submit"
                loading={isSubmitting || loading}
                loadingText={getSubmitButtonText()}
                className="flex-1"
              >
                {getSubmitButtonText()}
              </LoadingButton>

              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting || loading}
                  className="flex-1"
                >
                  取消
                </Button>
              )}
            </div>
          )}

          {/* 查看模式的关闭按钮 */}
          {isReadonly && onCancel && (
            <div className="flex justify-end pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
              >
                关闭
              </Button>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
};

export default CompetitorForm;
