# 部署指南

本文档详细说明如何将竞品分析管理系统部署到生产环境。

## 🚀 部署选项

### 1. Vercel 部署（推荐）

Vercel 是 Next.js 的官方部署平台，提供最佳的性能和开发体验。

#### 步骤：

1. **准备代码仓库**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **连接 Vercel**
   - 访问 [vercel.com](https://vercel.com)
   - 使用 GitHub 账号登录
   - 导入项目仓库

3. **配置环境变量**
   在 Vercel 项目设置中添加以下环境变量：
   ```
   DB_HOST=your-mysql-host
   DB_PORT=3306
   DB_USER=your-mysql-user
   DB_PASSWORD=your-mysql-password
   DB_NAME=competitor_analysis_db
   DB_CONNECTION_LIMIT=10
   DB_QUEUE_LIMIT=0
   DB_TIMEOUT=60000
   DB_ACQUIRE_TIMEOUT=60000
   ```

4. **部署**
   - Vercel 会自动检测 Next.js 项目
   - 点击 "Deploy" 开始部署
   - 部署完成后获得生产环境 URL

### 2. Docker 部署

使用 Docker 容器化部署，适合自托管环境。

#### 创建 Dockerfile：

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### 创建 docker-compose.yml：

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=password
      - DB_NAME=competitor_analysis_db
    depends_on:
      - mysql
    networks:
      - app-network

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=competitor_analysis_db
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./create_table.sql:/docker-entrypoint-initdb.d/create_table.sql
    networks:
      - app-network

volumes:
  mysql_data:

networks:
  app-network:
    driver: bridge
```

#### 部署命令：

```bash
# 构建并启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 3. 传统服务器部署

在 Ubuntu/CentOS 服务器上部署。

#### 环境准备：

```bash
# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2
sudo npm install -g pm2

# 安装 MySQL
sudo apt-get install mysql-server
```

#### 部署步骤：

```bash
# 1. 克隆代码
git clone <your-repo-url>
cd competitor-analysis-dashboard

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件

# 4. 构建项目
npm run build

# 5. 使用 PM2 启动
pm2 start npm --name "competitor-analysis" -- start
pm2 save
pm2 startup
```

## 🗄️ 数据库部署

### 云数据库服务

推荐使用云数据库服务：

1. **阿里云 RDS**
2. **腾讯云 CDB**
3. **AWS RDS**
4. **PlanetScale**（推荐用于 Vercel 部署）

### 自建 MySQL

如果使用自建 MySQL：

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE competitor_analysis_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入表结构
mysql -u root -p competitor_analysis_db < create_table.sql

# 创建应用用户
CREATE USER 'app_user'@'%' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON competitor_analysis_db.* TO 'app_user'@'%';
FLUSH PRIVILEGES;
```

## 🔒 安全配置

### 1. 环境变量安全

- 使用强密码
- 定期轮换密码
- 不要在代码中硬编码敏感信息

### 2. 数据库安全

```sql
-- 删除匿名用户
DELETE FROM mysql.user WHERE User='';

-- 删除测试数据库
DROP DATABASE IF EXISTS test;

-- 禁用远程 root 登录
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');

-- 刷新权限
FLUSH PRIVILEGES;
```

### 3. 网络安全

- 使用 HTTPS
- 配置防火墙
- 限制数据库访问 IP

## 📊 监控和日志

### 1. 应用监控

推荐使用：
- **Vercel Analytics**（Vercel 部署）
- **New Relic**
- **DataDog**

### 2. 数据库监控

- 监控连接数
- 查询性能
- 存储空间使用

### 3. 日志管理

```javascript
// 生产环境日志配置
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

## 🔄 CI/CD 配置

### GitHub Actions 示例：

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v20
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
```

## 🚨 故障排除

### 常见问题：

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库凭据
   - 确认防火墙设置

2. **构建失败**
   - 检查 Node.js 版本
   - 清除缓存：`npm cache clean --force`
   - 删除 node_modules 重新安装

3. **性能问题**
   - 启用数据库连接池
   - 优化查询语句
   - 添加适当的索引

## 📈 性能优化

### 1. 前端优化

- 启用 Next.js 图片优化
- 使用 CDN
- 启用 gzip 压缩

### 2. 数据库优化

```sql
-- 添加索引
CREATE INDEX idx_competitor_name ON Competitors(competitor_name);
CREATE INDEX idx_promotion_dates ON Promotions(sale_start_date, sale_end_date);
CREATE INDEX idx_active_competitors ON Competitors(is_active);
```

### 3. 缓存策略

- 使用 Redis 缓存热点数据
- 启用浏览器缓存
- 配置 CDN 缓存

---

部署完成后，记得测试所有功能确保正常运行！
