<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>竞品表单调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-panel {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .debug-step {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .debug-step.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .debug-step.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>竞品表单调试指南</h1>
    
    <div class="debug-panel">
        <div class="debug-title">🔍 问题诊断步骤</div>
        
        <div class="debug-step">
            <strong>步骤 1: 打开浏览器开发者工具</strong>
            <p>按 F12 或右键选择"检查元素"，切换到 Console 标签页</p>
        </div>
        
        <div class="debug-step">
            <strong>步骤 2: 清空控制台日志</strong>
            <p>点击控制台的清空按钮，确保看到的是新的日志信息</p>
        </div>
        
        <div class="debug-step">
            <strong>步骤 3: 尝试编辑竞品</strong>
            <p>在竞品管理页面点击编辑按钮，修改信息后点击"更新竞品"</p>
        </div>
        
        <div class="debug-step">
            <strong>步骤 4: 观察控制台输出</strong>
            <p>查看控制台中的调试信息，按照以下顺序应该出现：</p>
            <div class="code-block">
🖱️ [CompetitorForm] 提交按钮被点击
🚀 [CompetitorForm] 开始表单提交
📝 [CompetitorForm] 清理后的数据
📤 [CompetitorForm] 调用父组件onSubmit
🚀 [CompetitorsPage] 开始处理表单提交
📤 [CompetitorsPage] 发送API请求
📥 [CompetitorsPage] 收到API响应
📋 [CompetitorsPage] 解析响应数据
✅ [CompetitorsPage] 操作成功，关闭对话框并刷新列表
✅ [CompetitorForm] onSubmit执行成功
🏁 [CompetitorsPage] 表单提交完成，重置loading状态
            </div>
        </div>
    </div>
    
    <div class="debug-panel">
        <div class="debug-title">🚨 常见问题及解决方案</div>
        
        <div class="debug-step error">
            <strong>问题 1: 没有看到"提交按钮被点击"日志</strong>
            <p><strong>原因:</strong> 按钮点击事件没有触发</p>
            <p><strong>解决:</strong> 检查按钮是否被其他元素遮挡，或者是否有CSS问题</p>
        </div>
        
        <div class="debug-step error">
            <strong>问题 2: 看到"表单验证失败"日志</strong>
            <p><strong>原因:</strong> 表单数据不符合验证规则</p>
            <p><strong>解决:</strong> 检查必填字段是否为空，字段长度是否超限</p>
        </div>
        
        <div class="debug-step error">
            <strong>问题 3: 看到"表单提交异常"日志</strong>
            <p><strong>原因:</strong> 网络请求失败或API返回错误</p>
            <p><strong>解决:</strong> 检查网络连接，查看Network标签页的请求详情</p>
        </div>
        
        <div class="debug-step error">
            <strong>问题 4: API请求发送但返回错误</strong>
            <p><strong>原因:</strong> 服务器端处理失败</p>
            <p><strong>解决:</strong> 检查服务器日志，验证数据格式和权限</p>
        </div>
    </div>
    
    <div class="debug-panel">
        <div class="debug-title">🛠️ 快速测试工具</div>
        
        <button class="button" onclick="testConsoleLog()">测试控制台输出</button>
        <button class="button" onclick="testNetworkRequest()">测试网络请求</button>
        <button class="button" onclick="clearConsole()">清空控制台</button>
        
        <div id="test-output" style="margin-top: 15px;"></div>
    </div>
    
    <script>
        function testConsoleLog() {
            console.log('🧪 [Debug] 控制台测试 - 如果你看到这条消息，说明控制台工作正常');
            document.getElementById('test-output').innerHTML = '<div class="debug-step success">控制台测试成功！请查看Console标签页</div>';
        }
        
        function testNetworkRequest() {
            console.log('🧪 [Debug] 开始测试网络请求');
            fetch('/api/competitors?page=1&pageSize=1')
                .then(response => {
                    console.log('🧪 [Debug] 网络请求响应', response.status, response.statusText);
                    return response.json();
                })
                .then(data => {
                    console.log('🧪 [Debug] 网络请求数据', data);
                    document.getElementById('test-output').innerHTML = '<div class="debug-step success">网络请求测试成功！请查看Console标签页</div>';
                })
                .catch(error => {
                    console.error('🧪 [Debug] 网络请求失败', error);
                    document.getElementById('test-output').innerHTML = '<div class="debug-step error">网络请求测试失败！请查看Console标签页</div>';
                });
        }
        
        function clearConsole() {
            console.clear();
            document.getElementById('test-output').innerHTML = '<div class="debug-step">控制台已清空</div>';
        }
    </script>
</body>
</html>
