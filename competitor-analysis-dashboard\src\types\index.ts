/**
 * TypeScript类型定义统一导出文件
 * 
 * 功能说明：
 * 1. 统一导出所有类型定义，方便其他模块引用
 * 2. 提供类型定义的中央管理
 * 3. 避免循环依赖和重复导入
 */

// ============================================================================
// 数据库相关类型导出
// ============================================================================

export type {
  // 基础类型
  BaseRecord,
  PaginationParams,
  PaginatedResult,
  ApiResponse,

  // 竞品相关类型
  Competitor,
  CreateCompetitorInput,
  UpdateCompetitorInput,

  // 票种相关类型
  TicketType,
  CreateTicketTypeInput,
  UpdateTicketTypeInput,

  // 使用规则相关类型
  UsageRule,
  CreateUsageRuleInput,
  UpdateUsageRuleInput,

  // 促销活动相关类型
  Promotion,
  PromotionDetail,
  CreatePromotionInput,
  UpdatePromotionInput,
  BulkImportPromotionInput,
  BulkImportResult,
  BulkImportError,
  ExcelTemplateColumn,

  // 日历相关类型
  DailySales,
  DailySalesDetail,
  CreateDailySalesInput,
  DailyUsage,
  DailyUsageDetail,
  CreateDailyUsageInput,

  // 查询筛选相关类型
  PromotionFilters,
  CompetitorFilters,
  TicketTypeFilters,
} from './database';

// ============================================================================
// 表单相关类型导出
// ============================================================================

export type {
  // 验证相关类型
  FieldError,
  ValidationResult,

  // 表单状态类型
  FormState,

  // 表单数据类型
  CompetitorFormData,
  CompetitorFormValidation,
  TicketTypeFormData,
  TicketTypeFormValidation,
  PromotionFormData,
  PromotionFormValidation,
  DailySalesFormData,
  DailyUsageFormData,

  // 搜索筛选表单类型
  SearchFormData,
  FilterFormData,

  // 表单选项类型
  SelectOption,
  CompetitorOption,
  TicketTypeOption,

  // 表单操作类型
  FormMode,
  FormConfig,

  // 批量操作类型
  BatchOperationData,
  BatchOperationResult,
} from './forms';

export {
  // 枚举导出
  FormStatus,
  FormAction,
  BatchAction,
} from './forms';

// ============================================================================
// API相关类型导出
// ============================================================================

export type {
  // 基础API类型
  ApiError,
  ApiState,

  // 竞品API类型
  GetCompetitorsResponse,
  GetCompetitorResponse,
  CreateCompetitorRequest,
  CreateCompetitorResponse,
  UpdateCompetitorRequest,
  UpdateCompetitorResponse,
  DeleteCompetitorResponse,

  // 票种API类型
  GetTicketTypesResponse,
  GetTicketTypeResponse,
  CreateTicketTypeRequest,
  CreateTicketTypeResponse,
  UpdateTicketTypeRequest,
  UpdateTicketTypeResponse,
  DeleteTicketTypeResponse,

  // 促销活动API类型
  GetPromotionsResponse,
  GetPromotionResponse,
  CreatePromotionRequest,
  CreatePromotionResponse,
  UpdatePromotionRequest,
  UpdatePromotionResponse,
  DeletePromotionResponse,

  // 日历API类型
  GetDailySalesResponse,
  CreateDailySalesRequest,
  CreateDailySalesResponse,
  GetDailyUsageResponse,
  CreateDailyUsageRequest,
  CreateDailyUsageResponse,

  // 查询参数类型
  QueryParams,
  PromotionQueryParams,
  CompetitorQueryParams,
  TicketTypeQueryParams,

  // 批量操作API类型
  BatchDeleteRequest,
  BatchDeleteResponse,
  BatchOperationResponse,

  // 统计分析API类型
  StatisticsData,
  GetStatisticsResponse,

  // 导入导出API类型
  ExportRequest,
  ExportResponse,
  ExportDataResponse,
  ImportResult,
  ImportDataResponse,
} from './api';

export {
  // 枚举导出
  HttpMethod,
  ApiErrorType,
  ApiStatus,
} from './api';

// ============================================================================
// 常用类型别名定义
// ============================================================================

/**
 * 通用ID类型
 */
export type ID = string | number;

/**
 * 日期字符串类型（ISO格式）
 */
export type DateString = string;

/**
 * 价格类型（支持小数）
 */
export type Price = number;

/**
 * 折扣率类型（0-1之间的小数）
 */
export type DiscountRate = number;

/**
 * 通用回调函数类型
 */
export type Callback<T = void> = () => T;

/**
 * 异步回调函数类型
 */
export type AsyncCallback<T = void> = () => Promise<T>;

/**
 * 事件处理函数类型
 */
export type EventHandler<T = any> = (event: T) => void;

/**
 * 数据变更处理函数类型
 */
export type ChangeHandler<T = any> = (value: T) => void;

/**
 * 提交处理函数类型
 */
export type SubmitHandler<T = any> = (data: T) => Promise<void>;

// ============================================================================
// 工具类型定义
// ============================================================================

/**
 * 使所有属性可选的工具类型
 */
export type Partial<T> = {
  [P in keyof T]?: T[P];
};

/**
 * 使所有属性必需的工具类型
 */
export type Required<T> = {
  [P in keyof T]-?: T[P];
};

/**
 * 选择指定属性的工具类型
 */
export type Pick<T, K extends keyof T> = {
  [P in K]: T[P];
};

/**
 * 排除指定属性的工具类型
 */
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

/**
 * 键值对类型
 */
export type KeyValuePair<K = string, V = any> = {
  key: K;
  value: V;
};

/**
 * 字典类型
 */
export type Dictionary<T = any> = Record<string, T>;

/**
 * 可空类型
 */
export type Nullable<T> = T | null;

/**
 * 可选类型
 */
export type Optional<T> = T | undefined;

/**
 * 可空或可选类型
 */
export type Maybe<T> = T | null | undefined;

// ============================================================================
// 常量类型定义
// ============================================================================

/**
 * 表名常量类型
 */
export type TableName = 
  | 'Competitors'
  | 'TicketTypes'
  | 'Promotions'
  | 'DailySales'
  | 'DailyUsage';

/**
 * 排序方向类型
 */
export type SortDirection = 'ASC' | 'DESC';

/**
 * 数据状态类型
 */
export type DataStatus = 'active' | 'inactive' | 'deleted';

/**
 * 操作权限类型
 */
export type Permission = 'read' | 'write' | 'delete' | 'admin';

/**
 * 主题类型
 */
export type Theme = 'light' | 'dark' | 'system';

/**
 * 语言类型
 */
export type Language = 'zh-CN' | 'en-US';

// ============================================================================
// 组件Props相关类型定义
// ============================================================================

/**
 * React组件基础Props类型
 */
export interface BaseComponentProps {
  className?: string;        // CSS类名
  style?: React.CSSProperties; // 内联样式
  children?: React.ReactNode;  // 子组件
}

/**
 * 带有加载状态的组件Props类型
 */
export interface LoadingComponentProps extends BaseComponentProps {
  loading?: boolean;         // 是否显示加载状态
  loadingText?: string;      // 加载提示文本
}

/**
 * 带有错误状态的组件Props类型
 */
export interface ErrorComponentProps extends BaseComponentProps {
  error?: string | Error;    // 错误信息
  onRetry?: Callback;        // 重试回调
}

/**
 * 数据列表组件Props类型
 */
export interface DataListProps<T = any> extends BaseComponentProps {
  data: T[];                 // 数据列表
  loading?: boolean;         // 是否加载中
  error?: string;            // 错误信息
  onRefresh?: AsyncCallback; // 刷新回调
  onLoadMore?: AsyncCallback; // 加载更多回调
}
