/**
 * 促销活动批量导入对话框组件
 * 
 * 功能说明：
 * 1. 提供Excel文件上传功能
 * 2. 支持下载标准模板
 * 3. 显示上传进度和导入结果
 * 4. 提供详细的错误信息反馈
 */

'use client';

import React, { useState, useRef } from 'react';
import { Upload, Download, FileSpreadsheet, CheckCircle, XCircle, AlertCircle, X } from 'lucide-react';
import { Dialog, DialogTitle, DialogDescription, ScrollableFormDialog, ScrollableDialogHeader, ScrollableDialogBody, ScrollableDialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { BulkImportResult } from '@/types';

// ============================================================================
// 组件接口定义
// ============================================================================

interface BulkImportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportSuccess?: (result: BulkImportResult) => void;
}

interface ImportState {
  uploading: boolean;
  progress: number;
  result: BulkImportResult | null;
  error: string | null;
}

// ============================================================================
// 批量导入对话框组件
// ============================================================================

export function BulkImportDialog({ 
  open, 
  onOpenChange, 
  onImportSuccess 
}: BulkImportDialogProps) {
  // 组件状态
  const [importState, setImportState] = useState<ImportState>({
    uploading: false,
    progress: 0,
    result: null,
    error: null
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 重置状态
  const resetState = () => {
    setImportState({
      uploading: false,
      progress: 0,
      result: null,
      error: null
    });
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 关闭对话框
  const handleClose = () => {
    resetState();
    onOpenChange(false);
  };

  // 文件选择处理
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setImportState(prev => ({ ...prev, error: null, result: null }));
    }
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      console.log('📥 开始下载Excel模板');
      
      const response = await fetch('/api/promotions/template');
      if (!response.ok) {
        throw new Error('下载模板失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `促销活动导入模板_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('✅ 模板下载成功');
    } catch (error) {
      console.error('❌ 下载模板失败:', error);
      setImportState(prev => ({
        ...prev,
        error: '下载模板失败，请稍后重试'
      }));
    }
  };

  // 执行导入
  const handleImport = async () => {
    if (!selectedFile) {
      setImportState(prev => ({ ...prev, error: '请先选择要上传的文件' }));
      return;
    }

    try {
      setImportState(prev => ({
        ...prev,
        uploading: true,
        progress: 0,
        error: null,
        result: null
      }));

      console.log('📤 开始上传文件进行批量导入');

      // 创建表单数据
      const formData = new FormData();
      formData.append('file', selectedFile);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setImportState(prev => ({
          ...prev,
          progress: Math.min(prev.progress + 10, 90)
        }));
      }, 200);

      // 发送导入请求
      const response = await fetch('/api/promotions/bulk-import', {
        method: 'POST',
        body: formData
      });

      clearInterval(progressInterval);

      const result = await response.json();

      if (result.success) {
        setImportState(prev => ({
          ...prev,
          uploading: false,
          progress: 100,
          result: result.data
        }));

        console.log('✅ 批量导入成功');
        onImportSuccess?.(result.data);
      } else {
        throw new Error(result.error?.message || '导入失败');
      }

    } catch (error) {
      console.error('❌ 批量导入失败:', error);
      setImportState(prev => ({
        ...prev,
        uploading: false,
        progress: 0,
        error: error instanceof Error ? error.message : '导入失败'
      }));
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <ScrollableFormDialog className="max-w-2xl">
        <DialogTitle className="sr-only">批量导入促销活动</DialogTitle>
        <DialogDescription className="sr-only">
          通过上传Excel文件批量导入促销活动数据，提高数据录入效率
        </DialogDescription>

        <ScrollableDialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            批量导入促销活动
          </DialogTitle>
          <DialogDescription>
            通过上传Excel文件批量导入促销活动数据，提高数据录入效率
          </DialogDescription>
        </ScrollableDialogHeader>

        <ScrollableDialogBody>
          <div className="space-y-6">
          {/* 模板下载区域 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-blue-900 mb-2">第一步：下载标准模板</h4>
                <p className="text-sm text-blue-700 mb-3">
                  请先下载标准Excel模板，按照模板格式填写数据后再上传导入
                </p>
                <ul className="text-xs text-blue-600 space-y-1">
                  <li>• 竞品名称、票种名称、活动名称为必填字段</li>
                  <li>• 日期格式：YYYY-MM-DD（如：2024-01-01）</li>
                  <li>• 价格字段请填写数字，不要包含货币符号</li>
                  <li>• 建议一次导入不超过1000条记录</li>
                </ul>
              </div>
              <Button
                onClick={handleDownloadTemplate}
                variant="outline"
                size="sm"
                className="ml-4"
              >
                <Download className="h-4 w-4 mr-2" />
                下载模板
              </Button>
            </div>
          </div>

          {/* 文件上传区域 */}
          <div className="space-y-4">
            <h4 className="font-medium">第二步：上传Excel文件</h4>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                ref={fileInputRef}
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileSelect}
                className="hidden"
              />
              
              {selectedFile ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-center gap-2 text-green-600">
                    <CheckCircle className="h-5 w-5" />
                    <span className="font-medium">文件已选择</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    <div>{selectedFile.name}</div>
                    <div>{formatFileSize(selectedFile.size)}</div>
                  </div>
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    size="sm"
                  >
                    重新选择
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                  <div className="text-gray-600">
                    <p className="font-medium">点击选择Excel文件</p>
                    <p className="text-sm">支持 .xlsx 和 .xls 格式，最大10MB</p>
                  </div>
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                  >
                    选择文件
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* 上传进度 */}
          {importState.uploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>正在导入...</span>
                <span>{importState.progress}%</span>
              </div>
              <Progress value={importState.progress} className="w-full" />
            </div>
          )}

          {/* 错误信息 */}
          {importState.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-medium text-red-900">导入失败</h4>
                  <p className="text-sm text-red-700 mt-1">{importState.error}</p>
                </div>
              </div>
            </div>
          )}

          {/* 导入结果 */}
          {importState.result && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-medium text-green-900">导入完成</h4>
                  <div className="mt-2 space-y-2">
                    <div className="flex gap-4 text-sm">
                      <Badge variant="default">
                        总计 {importState.result.total_rows} 条
                      </Badge>
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        成功 {importState.result.success_count} 条
                      </Badge>
                      {importState.result.error_count > 0 && (
                        <Badge variant="destructive">
                          失败 {importState.result.error_count} 条
                        </Badge>
                      )}
                    </div>
                    
                    {/* 错误详情 */}
                    {importState.result.errors.length > 0 && (
                      <div className="mt-3">
                        <h5 className="font-medium text-red-900 mb-2">错误详情：</h5>
                        <div className="max-h-32 overflow-y-auto space-y-1">
                          {importState.result.errors.map((error, index) => (
                            <div key={index} className="text-xs text-red-700 bg-red-100 p-2 rounded">
                              第 {error.row_number} 行: {error.error_message}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          </div>
        </ScrollableDialogBody>

        <ScrollableDialogFooter>
          <Button
            onClick={handleImport}
            disabled={!selectedFile || importState.uploading || !!importState.result}
          >
            {importState.uploading ? '导入中...' : '开始导入'}
          </Button>
          <Button
            onClick={handleClose}
            variant="outline"
          >
            {importState.result ? '完成' : '取消'}
          </Button>
        </ScrollableDialogFooter>
      </ScrollableFormDialog>
    </Dialog>
  );
}
