USE competitor_analysis_db;
-- =================================================================
-- 阶段A：数据库结构平滑迁移脚本 (针对不支持 IF NOT EXISTS 的MySQL版本优化)
-- 目标：新增 usage_rule_id，并从旧的 usage_rules 字段迁移数据
-- =================================================================

-- 1. 确保 UsageRules 表存在并包含基础数据 (这段代码可以安全重复执行)
-- 这部分与之前相同，警告是正常的
CREATE TABLE IF NOT EXISTS UsageRules (
    rule_id          INT PRIMARY KEY AUTO_INCREMENT,
    rule_description VARCHAR(255) NOT NULL UNIQUE COMMENT '规则的文字描述',
    is_active        BOOLEAN DEFAULT TRUE COMMENT '该规则是否仍在使用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT IGNORE INTO UsageRules (rule_description) VALUES
('无限制'), ('仅限平日使用'), ('仅限周末使用'), ('周末及法定节假日不可用'), ('需至少提前1天预约');

-- 2. 在 Promotions 表中安全地添加新字段 usage_rule_id 和索引
-- 由于你的MySQL版本不支持 IF NOT EXISTS，我们需要手动检查字段是否存在
-- 2.1. 检查 usage_rule_id 列是否存在，如果不存在则添加
--      注意：这段代码块通常需要在一个支持条件判断的脚本中执行，
--      在普通SQL客户端中，你需要手动判断并选择执行。
--      如果你已经运行过，且报错了，说明列没有添加成功，可以直接运行下面的 ADD COLUMN。
--      如果后续再运行，可能会因为列已存在而报错，届时请注释掉此行。

-- **请手动检查 Promotions 表中是否已经有 usage_rule_id 字段**
-- 执行以下查询：
-- DESCRIBE Promotions;
-- 如果结果中没有 usage_rule_id 这一列，那么执行下面的 ALTER TABLE ADD COLUMN。
-- 如果已经有，请跳过下面的 ALTER TABLE ADD COLUMN，直接到 ADD INDEX。

ALTER TABLE Promotions
  ADD COLUMN usage_rule_id INT NULL COMMENT '关联到UsageRules表的外键' AFTER sales_channel;

-- 2.2. 添加索引 (同样，如果索引已存在会报错，你需要手动判断)
--      执行以下查询确认索引是否存在：
--      SHOW INDEX FROM Promotions WHERE Key_name = 'idx_usage_rule_id';
--      如果结果为空，则执行下面的 ALTER TABLE ADD INDEX。
--      如果已经有，请跳过下面的 ALTER TABLE ADD INDEX。

ALTER TABLE Promotions
  ADD INDEX idx_usage_rule_id (usage_rule_id);


-- 3. 数据迁移核心步骤：将旧文本规则迁移到新字典表，并回填ID
-- 这部分与之前相同，可以安全重复执行
INSERT IGNORE INTO UsageRules (rule_description)
SELECT DISTINCT LEFT(TRIM(usage_rules), 255) AS rule_description
FROM Promotions
WHERE usage_rules IS NOT NULL AND TRIM(usage_rules) <> '';
-- 临时禁用安全更新模式
SET SQL_SAFE_UPDATES = 0;
UPDATE Promotions p
JOIN UsageRules u ON u.rule_description = LEFT(TRIM(p.usage_rules), 255)
SET p.usage_rule_id = u.rule_id
WHERE p.usage_rules IS NOT NULL AND TRIM(p.usage_rules) <> '';
-- 重新启用安全更新模式 (非常重要，防止后续误操作)
SET SQL_SAFE_UPDATES = 1;
-- 4. 添加外键约束，正式建立两张表的关联关系 (在数据回填后操作，成功率最高)
-- !!! 解决 Error 1064 的关键步骤 !!!
-- 请手动跳过这条语句，因为它可能不被你的MySQL版本支持，并且此时外键很可能不存在
-- ALTER TABLE Promotions DROP FOREIGN KEY IF EXISTS fk_promo_usage_rule;
-- 直接执行添加外键约束的语句

ALTER TABLE Promotions
  ADD CONSTRAINT fk_promo_usage_rule
  FOREIGN KEY (usage_rule_id) REFERENCES UsageRules(rule_id)
  ON UPDATE CASCADE
  ON DELETE RESTRICT;

-- 5. (可选但推荐) 将之前没有规则的记录，统一关联到“无限制”
-- 这部分与之前相同，可以安全重复执行
SELECT @RULE_UNLIMITED_ID := rule_id FROM UsageRules WHERE rule_description = '无限制' LIMIT 1;

UPDATE Promotions
SET usage_rule_id = @RULE_UNLIMITED_ID
WHERE (usage_rule_id IS NULL) AND (usage_rules IS NULL OR TRIM(usage_rules) = '');

-- 操作完成提示
SELECT '阶段 A 数据库迁移脚本执行完毕！' AS Status;

-- 校验1：检查是否还有未被成功映射的记录 (预期结果: unmapped = 0)
SELECT COUNT(*) AS unmapped
FROM Promotions
WHERE (usage_rule_id IS NULL) AND (usage_rules IS NOT NULL AND TRIM(usage_rules) <> '');
-- 校验2：抽查几条数据，看看新旧字段是否对应正确
SELECT 
    promotion_id, 
    activity_name,
    usage_rules,  -- 旧字段
    usage_rule_id, -- 新字段
    (SELECT rule_description FROM UsageRules WHERE rule_id = p.usage_rule_id) AS new_rule_description -- 从新表反查出的描述
FROM Promotions p
ORDER BY promotion_id DESC
LIMIT 10;