# 数据库配置指南

## 📋 概述

本文档详细说明如何配置MySQL数据库连接，适合编程新手按步骤操作。

## 🔧 环境要求

- MySQL 8.0 或更高版本
- Node.js 18 或更高版本
- 已创建的数据库：`competitor_analysis_db`

## 📁 数据库位置

根据你提供的信息，数据库物理存储位置：
```
C:\ProgramData\MySQL\MySQL Server 8.0\Data\competitor_analysis_db
```

## ⚙️ 配置步骤

### 1. 复制环境变量文件

```bash
# 在项目根目录下
cp .env.example .env.local
```

### 2. 编辑 .env.local 文件

打开 `.env.local` 文件，修改以下配置：

```env
# 数据库连接信息
DB_HOST=localhost          # 数据库服务器地址
DB_PORT=3306              # MySQL端口号
DB_USER=root              # 数据库用户名
DB_PASSWORD=你的MySQL密码   # 替换为实际密码
DB_NAME=competitor_analysis_db

# 连接池配置（一般不需要修改）
DB_CONNECTION_LIMIT=10    # 最大连接数
DB_QUEUE_LIMIT=0         # 队列限制
DB_TIMEOUT=60000         # 超时时间（毫秒）
DB_ACQUIRE_TIMEOUT=60000 # 获取连接超时时间
```

### 3. 重要配置说明

#### 🔐 数据库密码配置
- 将 `DB_PASSWORD=your_password_here` 替换为你的MySQL root用户密码
- 如果你使用的不是root用户，请相应修改 `DB_USER`

#### 🌐 数据库地址配置
- 如果MySQL运行在本机，保持 `DB_HOST=localhost`
- 如果MySQL在其他服务器，修改为实际IP地址

#### 🔢 端口配置
- MySQL默认端口是3306，如果你修改过端口，请相应调整 `DB_PORT`

## 🧪 测试数据库连接

### 方法1：使用内置测试工具

创建测试文件 `test-db.js`：

```javascript
// test-db.js
const { testDatabaseConnection, validateDatabaseStructure } = require('./src/lib/database-test');

async function testDatabase() {
  console.log('开始测试数据库连接...');
  
  try {
    // 测试基本连接
    const connectionOk = await testDatabaseConnection();
    
    if (connectionOk) {
      console.log('✅ 数据库连接成功！');
      
      // 验证表结构
      const validation = await validateDatabaseStructure();
      
      if (validation.tablesOk) {
        console.log('✅ 数据库表结构完整！');
      } else {
        console.log('⚠️ 数据库表结构不完整，缺少以下表：');
        validation.missingTables.forEach(table => {
          console.log(`  - ${table}`);
        });
      }
    } else {
      console.log('❌ 数据库连接失败！');
    }
  } catch (error) {
    console.error('测试过程中发生错误：', error);
  }
}

testDatabase();
```

运行测试：
```bash
node test-db.js
```

### 方法2：使用MySQL命令行测试

```bash
# 测试MySQL连接
mysql -h localhost -P 3306 -u root -p

# 连接成功后，检查数据库
USE competitor_analysis_db;
SHOW TABLES;
```

## 🚨 常见问题解决

### 问题1：连接被拒绝 (Connection refused)

**可能原因：**
- MySQL服务未启动
- 端口号错误
- 防火墙阻止连接

**解决方案：**
```bash
# Windows - 检查MySQL服务状态
net start mysql80

# 或者通过服务管理器启动MySQL服务
```

### 问题2：访问被拒绝 (Access denied)

**可能原因：**
- 用户名或密码错误
- 用户没有访问权限

**解决方案：**
```sql
-- 重置root密码（在MySQL命令行中执行）
ALTER USER 'root'@'localhost' IDENTIFIED BY '新密码';
FLUSH PRIVILEGES;

-- 授予权限
GRANT ALL PRIVILEGES ON competitor_analysis_db.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

### 问题3：数据库不存在

**解决方案：**
```sql
-- 创建数据库
CREATE DATABASE competitor_analysis_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 然后运行你的 create_table.sql 文件
```

### 问题4：表不存在

**解决方案：**
```bash
# 在MySQL命令行中运行SQL文件
mysql -u root -p competitor_analysis_db < path/to/create_table.sql
```

## 🔒 安全建议

### 1. 环境变量安全
- 永远不要将 `.env.local` 文件提交到版本控制系统
- 使用强密码
- 定期更换数据库密码

### 2. 数据库用户权限
```sql
-- 创建专用的应用程序用户（推荐）
CREATE USER 'app_user'@'localhost' IDENTIFIED BY '强密码';
GRANT SELECT, INSERT, UPDATE, DELETE ON competitor_analysis_db.* TO 'app_user'@'localhost';
FLUSH PRIVILEGES;
```

然后在 `.env.local` 中使用：
```env
DB_USER=app_user
DB_PASSWORD=强密码
```

## 📊 连接池配置说明

```env
# 连接池配置详解
DB_CONNECTION_LIMIT=10    # 最大同时连接数，根据应用负载调整
DB_QUEUE_LIMIT=0         # 等待队列限制，0表示无限制
DB_TIMEOUT=60000         # 查询超时时间（60秒）
DB_ACQUIRE_TIMEOUT=60000 # 获取连接的超时时间（60秒）
```

### 性能调优建议：
- **小型应用**：CONNECTION_LIMIT = 5-10
- **中型应用**：CONNECTION_LIMIT = 10-20
- **大型应用**：CONNECTION_LIMIT = 20-50

## 🔄 下一步

配置完成后，你可以：
1. 运行数据库测试确认连接正常
2. 启动开发服务器：`npm run dev`
3. 访问应用程序开始使用CRUD功能

## 📞 获取帮助

如果遇到问题，请检查：
1. MySQL服务是否正在运行
2. 环境变量配置是否正确
3. 数据库和表是否已创建
4. 用户权限是否足够
