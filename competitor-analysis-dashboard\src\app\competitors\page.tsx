/**
 * 竞品管理页面
 * 
 * 功能说明：
 * 1. 展示竞品列表，支持分页、搜索、筛选
 * 2. 提供竞品的增删改查功能
 * 3. 响应式设计，适配移动端
 * 4. 批量操作支持
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Edit, Trash2, Eye } from 'lucide-react';
import { MainLayout, PageContainer } from '@/components/layout/main-layout';
import { DataTable, ColumnDef } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { LoadingPage } from '@/components/ui/loading';
import { DeleteConfirmDialog } from '@/components/ui/confirm-dialog';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { CompetitorForm } from '@/components/forms/competitor-form';
import { Competitor, PaginatedResult, CreateCompetitorInput, UpdateCompetitorInput } from '@/types';

// ============================================================================
// 页面状态接口定义
// ============================================================================

interface CompetitorsPageState {
  data: PaginatedResult<Competitor> | null;
  loading: boolean;
  error: string | null;
  searchText: string;
  currentPage: number;
  pageSize: number;
}

interface DialogState {
  type: 'create' | 'edit' | 'view' | null;
  open: boolean;
  data?: Competitor;
}

interface DeleteState {
  open: boolean;
  competitor?: Competitor;
  loading: boolean;
}

// ============================================================================
// 竞品管理页面组件
// ============================================================================

export default function CompetitorsPage() {
  // 页面状态
  const [state, setState] = useState<CompetitorsPageState>({
    data: null,
    loading: true,
    error: null,
    searchText: '',
    currentPage: 1,
    pageSize: 10
  });

  // 对话框状态
  const [dialogState, setDialogState] = useState<DialogState>({
    type: null,
    open: false
  });

  // 删除确认状态
  const [deleteState, setDeleteState] = useState<DeleteState>({
    open: false,
    loading: false
  });

  // 表单提交状态
  const [formLoading, setFormLoading] = useState(false);

  // 获取竞品列表
  const fetchCompetitors = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const params = new URLSearchParams({
        page: state.currentPage.toString(),
        pageSize: state.pageSize.toString(),
        ...(state.searchText && { competitor_name: state.searchText })
      });

      const response = await fetch(`/api/competitors?${params}`);
      const result = await response.json();

      if (result.success) {
        setState(prev => ({ ...prev, data: result.data, loading: false }));
      } else {
        setState(prev => ({ 
          ...prev, 
          error: result.error?.message || '获取数据失败', 
          loading: false 
        }));
      }
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : '网络错误', 
        loading: false 
      }));
    }
  };

  // 初始加载和依赖更新
  useEffect(() => {
    fetchCompetitors();
  }, [state.currentPage, state.pageSize]);

  // 搜索处理
  const handleSearch = () => {
    setState(prev => ({ ...prev, currentPage: 1 }));
    fetchCompetitors();
  };

  // 分页处理
  const handlePageChange = (page: number, pageSize: number) => {
    setState(prev => ({ ...prev, currentPage: page, pageSize }));
  };

  // 打开对话框
  const openDialog = (type: 'create' | 'edit' | 'view', data?: Competitor) => {
    setDialogState({ type, open: true, data });
  };

  // 关闭对话框
  const closeDialog = () => {
    setDialogState({ type: null, open: false, data: undefined });
  };

  // 表单提交处理
  const handleFormSubmit = async (data: CreateCompetitorInput | UpdateCompetitorInput) => {
    console.log('🚀 [CompetitorsPage] 开始处理表单提交', {
      dialogType: dialogState.type,
      competitorId: dialogState.data?.competitor_id,
      data
    });

    try {
      setFormLoading(true);

      const isEdit = dialogState.type === 'edit';
      const url = isEdit ? `/api/competitors/${dialogState.data?.competitor_id}` : '/api/competitors';
      const method = isEdit ? 'PUT' : 'POST';

      console.log('📤 [CompetitorsPage] 发送API请求', { url, method, data });

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      console.log('📥 [CompetitorsPage] 收到API响应', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      const result = await response.json();
      console.log('📋 [CompetitorsPage] 解析响应数据', result);

      if (result.success) {
        console.log('✅ [CompetitorsPage] 操作成功，关闭对话框并刷新列表');
        closeDialog();
        fetchCompetitors(); // 刷新列表
      } else {
        console.error('❌ [CompetitorsPage] API返回错误', result.error);
        throw new Error(result.error?.message || '操作失败');
      }
    } catch (error) {
      console.error('💥 [CompetitorsPage] 表单提交异常', error);
      throw error; // 让表单组件处理错误显示
    } finally {
      console.log('🏁 [CompetitorsPage] 表单提交完成，重置loading状态');
      setFormLoading(false);
    }
  };

  // 删除处理
  const handleDelete = async () => {
    if (!deleteState.competitor) return;

    try {
      setDeleteState(prev => ({ ...prev, loading: true }));

      const response = await fetch(`/api/competitors/${deleteState.competitor.competitor_id}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        setDeleteState({ open: false, loading: false });
        fetchCompetitors(); // 刷新列表
      } else {
        throw new Error(result.error?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      setDeleteState(prev => ({ ...prev, loading: false }));
    }
  };

  // 表格列定义
  const columns: ColumnDef<Competitor>[] = [
    {
      key: 'competitor_name',
      title: '竞品名称',
      dataIndex: 'competitor_name',
      sortable: true,
      render: (value, record) => (
        <div className="font-medium">{value}</div>
      )
    },
    {
      key: 'city',
      title: '所在城市',
      dataIndex: 'city',
      render: (value) => value || '-'
    },
    {
      key: 'park_type',
      title: '园区类型',
      dataIndex: 'park_type',
      render: (value) => value || '-'
    },
    {
      key: 'is_active',
      title: '状态',
      dataIndex: 'is_active',
      align: 'center',
      render: (value) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? '活跃' : '非活跃'}
        </Badge>
      )
    },
    {
      key: 'actions',
      title: '操作',
      align: 'center',
      render: (_, record) => (
        <div className="flex gap-2 justify-center">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => openDialog('view', record)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => openDialog('edit', record)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setDeleteState({ open: true, competitor: record, loading: false })}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  // 加载状态
  if (state.loading && !state.data) {
    return (
      <MainLayout currentPath="/competitors">
        <LoadingPage text="加载竞品数据..." />
      </MainLayout>
    );
  }

  return (
    <MainLayout currentPath="/competitors">
      <PageContainer
        title="竞品管理"
        description="管理竞品信息，包括竞品的基本信息和状态"
        actions={
          <Button onClick={() => openDialog('create')}>
            <Plus className="h-4 w-4 mr-2" />
            添加竞品
          </Button>
        }
      >
        {/* 搜索和筛选 */}
        <div className="flex gap-4 mb-6">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="搜索竞品名称..."
                value={state.searchText}
                onChange={(e) => setState(prev => ({ ...prev, searchText: e.target.value }))}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-9"
              />
            </div>
          </div>
          <Button onClick={handleSearch} variant="outline">
            搜索
          </Button>
        </div>

        {/* 数据表格 */}
        <DataTable
          columns={columns}
          data={state.data?.data || []}
          loading={state.loading}
          pagination={state.data ? {
            current: state.currentPage,
            pageSize: state.pageSize,
            total: state.data.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: handlePageChange
          } : undefined}
          emptyText="暂无竞品数据"
        />

        {/* 表单对话框 */}
        <Dialog open={dialogState.open} onOpenChange={closeDialog}>
          <DialogContent className="max-w-2xl">
            <DialogTitle className="sr-only">
              {dialogState.type === 'create' && '添加竞品'}
              {dialogState.type === 'edit' && '编辑竞品'}
              {dialogState.type === 'view' && '查看竞品'}
            </DialogTitle>
            <DialogDescription className="sr-only">
              {dialogState.type === 'create' && '创建新的竞品信息'}
              {dialogState.type === 'edit' && '编辑现有竞品信息'}
              {dialogState.type === 'view' && '查看竞品详细信息'}
            </DialogDescription>
            {dialogState.type && (
              <CompetitorForm
                mode={dialogState.type}
                initialData={dialogState.data}
                onSubmit={handleFormSubmit}
                onCancel={closeDialog}
                loading={formLoading}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* 删除确认对话框 */}
        <DeleteConfirmDialog
          open={deleteState.open}
          onOpenChange={(open) => setDeleteState(prev => ({ ...prev, open }))}
          itemName={deleteState.competitor?.competitor_name}
          itemType="竞品"
          onConfirm={handleDelete}
          loading={deleteState.loading}
          additionalInfo={deleteState.competitor ? {
            '竞品名称': deleteState.competitor.competitor_name,
            '所在城市': deleteState.competitor.city || '-',
            '园区类型': deleteState.competitor.park_type || '-',
            '状态': deleteState.competitor.is_active ? '活跃' : '非活跃'
          } : undefined}
        />
      </PageContainer>
    </MainLayout>
  );
}
