/**
 * 首页仪表板页面
 * 
 * 功能说明：
 * 1. 显示系统整体统计信息
 * 2. 展示关键数据指标
 * 3. 提供快速导航入口
 * 4. 响应式设计
 */

'use client';

import React, { useEffect, useState } from 'react';
import { Users, Ticket, Database, TrendingUp } from 'lucide-react';
import { MainLayout, PageContainer, CardContainer } from '@/components/layout/main-layout';
import { LoadingPage } from '@/components/ui/loading';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// ============================================================================
// 统计数据接口定义
// ============================================================================

interface DashboardStats {
  competitors: {
    total: number;
    active: number;
    inactive: number;
    cityCount: number;
    parkTypeCount: number;
  };
  ticketTypes: {
    total: number;
    categoryCount: number;
    withPromotions: number;
    withoutPromotions: number;
  };
  promotions: {
    total: number;
    activePromotions: number;
    expiredPromotions: number;
    avgDiscountRate: number;
    avgPrice: number;
    totalCompetitors: number;
    totalTicketTypes: number;
  };
  summary: {
    totalRecords: number;
    lastUpdated: string;
  };
}

// ============================================================================
// 统计卡片组件
// ============================================================================

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ComponentType<{ className?: string }>;
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  description,
  icon: Icon,
  color = 'blue'
}) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    yellow: 'bg-yellow-50 text-yellow-600',
    red: 'bg-red-50 text-red-600',
    purple: 'bg-purple-50 text-purple-600'
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">
          {title}
        </CardTitle>
        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
          <Icon className="h-4 w-4" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>
        {description && (
          <p className="text-xs text-gray-500 mt-1">
            {description}
          </p>
        )}
      </CardContent>
    </Card>
  );
};

// ============================================================================
// 首页组件实现
// ============================================================================

export default function HomePage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取统计数据
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/statistics');
        
        if (!response.ok) {
          throw new Error('获取统计数据失败');
        }

        const result = await response.json();
        if (result.success) {
          setStats(result.data);
        } else {
          throw new Error(result.error?.message || '获取统计数据失败');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '未知错误');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  // 加载状态
  if (loading) {
    return (
      <MainLayout>
        <LoadingPage text="加载仪表板数据..." />
      </MainLayout>
    );
  }

  // 错误状态
  if (error || !stats) {
    return (
      <MainLayout>
        <PageContainer>
          <div className="text-center py-12">
            <div className="text-red-500 text-lg font-medium mb-4">
              {error || '数据加载失败'}
            </div>
            <Button onClick={() => window.location.reload()}>
              重新加载
            </Button>
          </div>
        </PageContainer>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageContainer
        title="仪表板"
        description="竞品分析系统数据概览"
      >
        <div className="space-y-6">
          {/* 核心指标卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="竞品总数"
              value={stats.competitors.total}
              description={`活跃: ${stats.competitors.active} | 非活跃: ${stats.competitors.inactive}`}
              icon={Users}
              color="blue"
            />
            
            <StatCard
              title="票种总数"
              value={stats.ticketTypes.total}
              description={`分类数: ${stats.ticketTypes.categoryCount}`}
              icon={Ticket}
              color="green"
            />
            
            <StatCard
              title="促销活动"
              value={stats.promotions.total}
              description={`活跃: ${stats.promotions.activePromotions} | 已过期: ${stats.promotions.expiredPromotions}`}
              icon={Database}
              color="purple"
            />
            
            <StatCard
              title="平均折扣率"
              value={`${(stats.promotions.avgDiscountRate * 100).toFixed(1)}%`}
              description={`平均价格: ¥${stats.promotions.avgPrice.toFixed(2)}`}
              icon={TrendingUp}
              color="yellow"
            />
          </div>

          {/* 详细统计信息 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 竞品分析 */}
            <CardContainer
              title="竞品分析"
              description="竞品数据分布情况"
            >
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">总竞品数</span>
                  <span className="font-medium">{stats.competitors.total}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">活跃竞品</span>
                  <Badge variant="default">{stats.competitors.active}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">非活跃竞品</span>
                  <Badge variant="secondary">{stats.competitors.inactive}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">覆盖城市数</span>
                  <span className="font-medium">{stats.competitors.cityCount}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">园区类型数</span>
                  <span className="font-medium">{stats.competitors.parkTypeCount}</span>
                </div>
              </div>
            </CardContainer>

            {/* 票种分析 */}
            <CardContainer
              title="票种分析"
              description="票种数据分布情况"
            >
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">总票种数</span>
                  <span className="font-medium">{stats.ticketTypes.total}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">票种分类数</span>
                  <span className="font-medium">{stats.ticketTypes.categoryCount}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">有促销活动</span>
                  <Badge variant="default">{stats.ticketTypes.withPromotions}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">无促销活动</span>
                  <Badge variant="secondary">{stats.ticketTypes.withoutPromotions}</Badge>
                </div>
              </div>
            </CardContainer>
          </div>

          {/* 快速操作 */}
          <CardContainer
            title="快速操作"
            description="常用功能快速入口"
          >
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button 
                variant="outline" 
                className="h-20 flex flex-col items-center justify-center space-y-2"
                onClick={() => window.location.href = '/competitors'}
              >
                <Users className="h-6 w-6" />
                <span>管理竞品</span>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20 flex flex-col items-center justify-center space-y-2"
                onClick={() => window.location.href = '/ticket-types'}
              >
                <Ticket className="h-6 w-6" />
                <span>管理票种</span>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20 flex flex-col items-center justify-center space-y-2"
                onClick={() => window.location.href = '/promotions'}
              >
                <Database className="h-6 w-6" />
                <span>管理促销活动</span>
              </Button>
            </div>
          </CardContainer>

          {/* 系统信息 */}
          <div className="text-center text-sm text-gray-500">
            <p>
              总记录数: {stats.summary.totalRecords.toLocaleString()} | 
              最后更新: {new Date(stats.summary.lastUpdated).toLocaleString()}
            </p>
          </div>
        </div>
      </PageContainer>
    </MainLayout>
  );
}
