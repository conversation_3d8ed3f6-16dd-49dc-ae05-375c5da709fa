/**
 * API工具函数
 * 
 * 功能说明：
 * 1. 提供统一的API响应格式
 * 2. 实现请求验证和错误处理
 * 3. 提供通用的API辅助函数
 * 4. 支持分页参数解析和验证
 */

import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse, ApiError, ApiErrorType, PaginationParams } from '@/types';

// ============================================================================
// API响应工具函数
// ============================================================================

/**
 * 创建成功响应
 * @param data 响应数据
 * @param message 响应消息
 * @returns API响应
 */
export function createSuccessResponse<T>(
  data?: T,
  message?: string
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString()
  };

  return NextResponse.json(response);
}

/**
 * 创建错误响应
 * @param error 错误信息
 * @param status HTTP状态码
 * @returns API响应
 */
export function createErrorResponse(
  error: ApiError | string,
  status: number = 500
): NextResponse<ApiResponse> {
  const apiError: ApiError = typeof error === 'string' 
    ? {
        type: ApiErrorType.UNKNOWN_ERROR,
        message: error,
        timestamp: new Date().toISOString()
      }
    : error;

  const response: ApiResponse = {
    success: false,
    error: apiError,
    timestamp: new Date().toISOString()
  };

  return NextResponse.json(response, { status });
}

/**
 * 创建验证错误响应
 * @param message 错误消息
 * @param details 错误详情
 * @returns API响应
 */
export function createValidationErrorResponse(
  message: string,
  details?: any
): NextResponse<ApiResponse> {
  const error: ApiError = {
    type: ApiErrorType.VALIDATION_ERROR,
    message,
    details,
    timestamp: new Date().toISOString()
  };

  return createErrorResponse(error, 400);
}

/**
 * 创建未找到错误响应
 * @param resource 资源名称
 * @param id 资源ID
 * @returns API响应
 */
export function createNotFoundResponse(
  resource: string,
  id?: string | number
): NextResponse<ApiResponse> {
  const message = id 
    ? `${resource} with ID ${id} not found`
    : `${resource} not found`;

  const error: ApiError = {
    type: ApiErrorType.NOT_FOUND_ERROR,
    message,
    timestamp: new Date().toISOString()
  };

  return createErrorResponse(error, 404);
}

// ============================================================================
// 请求参数解析工具函数
// ============================================================================

/**
 * 解析分页参数
 * @param request 请求对象
 * @returns 分页参数
 */
export function parsePaginationParams(request: NextRequest): PaginationParams {
  const { searchParams } = new URL(request.url);

  const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
  const pageSize = Math.min(100, Math.max(1, parseInt(searchParams.get('pageSize') || '10')));
  const sortBy = searchParams.get('sortBy') || undefined;
  const sortOrder = (searchParams.get('sortOrder')?.toUpperCase() as 'ASC' | 'DESC') || 'ASC';

  return {
    page,
    pageSize,
    sortBy,
    sortOrder
  };
}

/**
 * 解析查询参数为对象
 * @param request 请求对象
 * @param excludeKeys 要排除的键
 * @returns 查询参数对象
 */
export function parseQueryParams(
  request: NextRequest,
  excludeKeys: string[] = ['page', 'pageSize', 'sortBy', 'sortOrder']
): Record<string, any> {
  const { searchParams } = new URL(request.url);
  const params: Record<string, any> = {};

  for (const [key, value] of searchParams.entries()) {
    if (!excludeKeys.includes(key) && value) {
      // 尝试解析数字
      if (/^\d+$/.test(value)) {
        params[key] = parseInt(value);
      }
      // 尝试解析布尔值
      else if (value === 'true' || value === 'false') {
        params[key] = value === 'true';
      }
      // 字符串值
      else {
        params[key] = value;
      }
    }
  }

  return params;
}

/**
 * 解析请求体JSON数据
 * @param request 请求对象
 * @returns JSON数据
 */
export async function parseRequestBody<T = any>(request: NextRequest): Promise<T> {
  try {
    const body = await request.json();
    return body as T;
  } catch (error) {
    throw new Error('Invalid JSON in request body');
  }
}

// ============================================================================
// 请求验证工具函数
// ============================================================================

/**
 * 验证必需字段
 * @param data 数据对象
 * @param requiredFields 必需字段列表
 * @returns 验证结果
 */
export function validateRequiredFields(
  data: any,
  requiredFields: string[]
): { isValid: boolean; missingFields: string[] } {
  const missingFields: string[] = [];

  for (const field of requiredFields) {
    if (data[field] === undefined || data[field] === null || data[field] === '') {
      missingFields.push(field);
    }
  }

  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}

/**
 * 验证数字范围
 * @param value 数值
 * @param min 最小值
 * @param max 最大值
 * @returns 是否有效
 */
export function validateNumberRange(
  value: number,
  min?: number,
  max?: number
): boolean {
  if (typeof value !== 'number' || isNaN(value)) {
    return false;
  }

  if (min !== undefined && value < min) {
    return false;
  }

  if (max !== undefined && value > max) {
    return false;
  }

  return true;
}

/**
 * 验证字符串长度
 * @param value 字符串值
 * @param minLength 最小长度
 * @param maxLength 最大长度
 * @returns 是否有效
 */
export function validateStringLength(
  value: string,
  minLength?: number,
  maxLength?: number
): boolean {
  if (typeof value !== 'string') {
    return false;
  }

  if (minLength !== undefined && value.length < minLength) {
    return false;
  }

  if (maxLength !== undefined && value.length > maxLength) {
    return false;
  }

  return true;
}

/**
 * 验证日期格式
 * @param dateString 日期字符串
 * @returns 是否有效
 */
export function validateDateFormat(dateString: string): boolean {
  if (!dateString) return false;
  
  const date = new Date(dateString);
  return !isNaN(date.getTime());
}

/**
 * 验证URL格式
 * @param url URL字符串
 * @returns 是否有效
 */
export function validateUrlFormat(url: string): boolean {
  if (!url) return true; // 空URL被认为是有效的（可选字段）
  
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// ============================================================================
// 错误处理工具函数
// ============================================================================

/**
 * 包装API处理函数，提供统一的错误处理
 * @param handler API处理函数
 * @returns 包装后的处理函数
 */
export function withErrorHandling(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context);
    } catch (error) {
      console.error('API处理函数发生错误:', error);

      // 数据库错误
      if (error instanceof Error && error.message.includes('数据库')) {
        const apiError: ApiError = {
          type: ApiErrorType.DATABASE_ERROR,
          message: '数据库操作失败',
          details: error.message,
          timestamp: new Date().toISOString()
        };
        return createErrorResponse(apiError, 500);
      }

      // 验证错误
      if (error instanceof Error && error.message.includes('验证')) {
        const apiError: ApiError = {
          type: ApiErrorType.VALIDATION_ERROR,
          message: error.message,
          timestamp: new Date().toISOString()
        };
        return createErrorResponse(apiError, 400);
      }

      // 通用错误
      const apiError: ApiError = {
        type: ApiErrorType.SERVER_ERROR,
        message: error instanceof Error ? error.message : '服务器内部错误',
        timestamp: new Date().toISOString()
      };

      return createErrorResponse(apiError, 500);
    }
  };
}

// ============================================================================
// HTTP方法验证工具函数
// ============================================================================

/**
 * 验证HTTP方法
 * @param request 请求对象
 * @param allowedMethods 允许的方法列表
 * @returns 验证结果
 */
export function validateHttpMethod(
  request: NextRequest,
  allowedMethods: string[]
): boolean {
  return allowedMethods.includes(request.method);
}

/**
 * 创建方法不允许的响应
 * @param allowedMethods 允许的方法列表
 * @returns API响应
 */
export function createMethodNotAllowedResponse(
  allowedMethods: string[]
): NextResponse<ApiResponse> {
  const error: ApiError = {
    type: ApiErrorType.VALIDATION_ERROR,
    message: `Method not allowed. Allowed methods: ${allowedMethods.join(', ')}`,
    timestamp: new Date().toISOString()
  };

  const response = NextResponse.json({
    success: false,
    error,
    timestamp: new Date().toISOString()
  }, { status: 405 });

  response.headers.set('Allow', allowedMethods.join(', '));
  return response;
}
