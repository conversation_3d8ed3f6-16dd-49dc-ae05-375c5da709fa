/**
 * 数据库连接测试工具
 * 
 * 功能说明：
 * 1. 测试数据库连接是否正常
 * 2. 验证数据库表结构是否存在
 * 3. 提供数据库初始化检查功能
 */

import { executeQuery, checkDatabaseHealth, getConnectionPool } from './database';

/**
 * 数据库表信息接口
 */
interface TableInfo {
  tableName: string;
  tableComment: string;
  expectedColumns: string[];
}

/**
 * 预期的数据库表结构
 */
const EXPECTED_TABLES: TableInfo[] = [
  {
    tableName: 'Competitors',
    tableComment: '竞品字典表',
    expectedColumns: ['competitor_id', 'competitor_name', 'city', 'park_type', 'is_active']
  },
  {
    tableName: 'TicketTypes',
    tableComment: '票种字典表',
    expectedColumns: ['ticket_type_id', 'ticket_type_name', 'category']
  },
  {
    tableName: 'Promotions',
    tableComment: '促销活动主表',
    expectedColumns: [
      'promotion_id', 'competitor_id', 'ticket_type_id', 'activity_name',
      'rack_rate', 'promo_price', 'sale_start_date', 'sale_end_date',
      'use_start_date', 'use_end_date', 'sales_channel', 'usage_rule_id',
      'data_source_url', 'entry_date', 'remarks'
    ]
  },
  {
    tableName: 'UsageRules',
    tableComment: '使用规则字典表',
    expectedColumns: ['rule_id', 'rule_description', 'is_active']
  },
  {
    tableName: 'DailySales',
    tableComment: '促销日历-销售维度',
    expectedColumns: ['sales_date', 'promotion_id', 'promo_price', 'rack_rate', 'discount_rate']
  },
  {
    tableName: 'DailyUsage',
    tableComment: '促销日历-核销维度',
    expectedColumns: ['usage_date', 'promotion_id', 'promo_price', 'rack_rate', 'discount_rate']
  }
];

/**
 * 测试数据库基本连接
 */
export const testDatabaseConnection = async (): Promise<boolean> => {
  try {
    console.log('🔍 开始测试数据库连接...');
    
    const isHealthy = await checkDatabaseHealth();
    
    if (isHealthy) {
      console.log('✅ 数据库连接测试成功');
      return true;
    } else {
      console.log('❌ 数据库连接测试失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 数据库连接测试异常:', error);
    return false;
  }
};

/**
 * 检查数据库表是否存在
 */
export const checkTablesExist = async (): Promise<{ [tableName: string]: boolean }> => {
  try {
    console.log('🔍 检查数据库表结构...');
    
    const query = `
      SELECT TABLE_NAME, TABLE_COMMENT 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? 
      ORDER BY TABLE_NAME
    `;
    
    const tables = await executeQuery<{ TABLE_NAME: string; TABLE_COMMENT: string }>(
      query,
      [process.env.DB_NAME]
    );
    
    const existingTables = new Set(tables.map(t => t.TABLE_NAME));
    const result: { [tableName: string]: boolean } = {};
    
    console.log('📋 数据库中现有的表:');
    tables.forEach(table => {
      console.log(`  - ${table.TABLE_NAME}: ${table.TABLE_COMMENT}`);
    });
    
    console.log('\n🔍 检查预期表结构:');
    EXPECTED_TABLES.forEach(expectedTable => {
      const exists = existingTables.has(expectedTable.tableName);
      result[expectedTable.tableName] = exists;
      
      const status = exists ? '✅' : '❌';
      console.log(`  ${status} ${expectedTable.tableName} (${expectedTable.tableComment})`);
    });
    
    return result;
  } catch (error) {
    console.error('❌ 检查数据库表结构失败:', error);
    throw error;
  }
};

/**
 * 检查表的列结构
 */
export const checkTableColumns = async (tableName: string): Promise<string[]> => {
  try {
    const query = `
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY ORDINAL_POSITION
    `;
    
    const columns = await executeQuery<{ COLUMN_NAME: string }>(
      query,
      [process.env.DB_NAME, tableName]
    );
    
    return columns.map(col => col.COLUMN_NAME);
  } catch (error) {
    console.error(`❌ 检查表 ${tableName} 的列结构失败:`, error);
    throw error;
  }
};

/**
 * 完整的数据库结构验证
 */
export const validateDatabaseStructure = async (): Promise<{
  connectionOk: boolean;
  tablesOk: boolean;
  missingTables: string[];
  details: { [tableName: string]: { exists: boolean; columns?: string[] } };
}> => {
  try {
    console.log('🔍 开始完整的数据库结构验证...');
    
    // 1. 测试连接
    const connectionOk = await testDatabaseConnection();
    if (!connectionOk) {
      return {
        connectionOk: false,
        tablesOk: false,
        missingTables: EXPECTED_TABLES.map(t => t.tableName),
        details: {}
      };
    }
    
    // 2. 检查表存在性
    const tableExists = await checkTablesExist();
    const missingTables = EXPECTED_TABLES
      .filter(table => !tableExists[table.tableName])
      .map(table => table.tableName);
    
    const tablesOk = missingTables.length === 0;
    
    // 3. 检查每个表的列结构
    const details: { [tableName: string]: { exists: boolean; columns?: string[] } } = {};
    
    for (const expectedTable of EXPECTED_TABLES) {
      const exists = tableExists[expectedTable.tableName];
      details[expectedTable.tableName] = { exists };
      
      if (exists) {
        try {
          const columns = await checkTableColumns(expectedTable.tableName);
          details[expectedTable.tableName].columns = columns;
          
          console.log(`📋 表 ${expectedTable.tableName} 的列结构:`);
          columns.forEach(col => console.log(`    - ${col}`));
        } catch (error) {
          console.error(`❌ 无法获取表 ${expectedTable.tableName} 的列信息:`, error);
        }
      }
    }
    
    // 4. 输出验证结果
    console.log('\n📊 数据库结构验证结果:');
    console.log(`  🔗 数据库连接: ${connectionOk ? '✅ 正常' : '❌ 失败'}`);
    console.log(`  📋 表结构完整性: ${tablesOk ? '✅ 完整' : '❌ 不完整'}`);
    
    if (missingTables.length > 0) {
      console.log('  ⚠️  缺少的表:');
      missingTables.forEach(table => console.log(`    - ${table}`));
    }
    
    return {
      connectionOk,
      tablesOk,
      missingTables,
      details
    };
  } catch (error) {
    console.error('❌ 数据库结构验证失败:', error);
    throw error;
  }
};

/**
 * 获取数据库连接池状态信息
 */
export const getDatabasePoolStatus = (): any => {
  try {
    const pool = getConnectionPool();
    
    // 注意：mysql2的连接池可能没有直接暴露这些属性
    // 这里提供一个基础的状态检查框架
    const status = {
      poolExists: !!pool,
      timestamp: new Date().toISOString(),
      // 可以根据mysql2的实际API添加更多状态信息
    };
    
    console.log('📊 数据库连接池状态:', status);
    return status;
  } catch (error) {
    console.error('❌ 获取数据库连接池状态失败:', error);
    return { error: error instanceof Error ? error.message : '未知错误' };
  }
};
