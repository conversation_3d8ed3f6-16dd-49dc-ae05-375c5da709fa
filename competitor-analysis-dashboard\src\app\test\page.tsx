/**
 * 测试页面
 * 
 * 功能说明：
 * 1. 测试UI组件是否正常工作
 * 2. 验证数据库连接
 * 3. 检查API路由
 */

'use client';

import React, { useState } from 'react';
import { MainLayout, PageContainer, CardContainer } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { LoadingButton, LoadingInline } from '@/components/ui/loading';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestPage() {
  const [loading, setLoading] = useState(false);
  const [apiResult, setApiResult] = useState<string>('');

  const testAPI = async (endpoint: string) => {
    setLoading(true);
    try {
      const response = await fetch(endpoint);
      const data = await response.json();
      setApiResult(`${endpoint}: ${response.ok ? '✅ 成功' : '❌ 失败'} - ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      setApiResult(`${endpoint}: ❌ 错误 - ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout currentPath="/test">
      <PageContainer
        title="系统测试"
        description="测试各个组件和功能是否正常工作"
      >
        <div className="space-y-6">
          {/* UI组件测试 */}
          <CardContainer title="UI组件测试">
            <div className="space-y-4">
              <div className="flex gap-2 flex-wrap">
                <Button variant="default">默认按钮</Button>
                <Button variant="outline">轮廓按钮</Button>
                <Button variant="secondary">次要按钮</Button>
                <LoadingButton loading={loading}>加载按钮</LoadingButton>
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Badge variant="default">默认标签</Badge>
                <Badge variant="secondary">次要标签</Badge>
                <Badge variant="destructive">危险标签</Badge>
              </div>

              {loading && <LoadingInline text="测试加载中..." />}
            </div>
          </CardContainer>

          {/* API测试 */}
          <CardContainer title="API接口测试">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Button 
                  onClick={() => testAPI('/api/statistics')}
                  disabled={loading}
                  variant="outline"
                >
                  测试统计API
                </Button>
                
                <Button 
                  onClick={() => testAPI('/api/competitors?page=1&pageSize=5')}
                  disabled={loading}
                  variant="outline"
                >
                  测试竞品API
                </Button>
                
                <Button 
                  onClick={() => testAPI('/api/ticket-types?page=1&pageSize=5')}
                  disabled={loading}
                  variant="outline"
                >
                  测试票种API
                </Button>
                
                <Button 
                  onClick={() => testAPI('/api/promotions?page=1&pageSize=5')}
                  disabled={loading}
                  variant="outline"
                >
                  测试促销API
                </Button>
              </div>

              {apiResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">API测试结果</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto max-h-64">
                      {apiResult}
                    </pre>
                  </CardContent>
                </Card>
              )}
            </div>
          </CardContainer>

          {/* 数据库连接测试 */}
          <CardContainer title="数据库连接测试">
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                点击下面的按钮测试数据库连接是否正常：
              </p>
              
              <Button 
                onClick={() => testAPI('/api/statistics')}
                disabled={loading}
              >
                测试数据库连接
              </Button>

              <div className="text-sm text-gray-500">
                <p>如果数据库连接正常，应该能够获取到统计数据。</p>
                <p>如果连接失败，请检查：</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>MySQL服务是否启动</li>
                  <li>.env.local文件配置是否正确</li>
                  <li>数据库和表是否已创建</li>
                  <li>用户权限是否足够</li>
                </ul>
              </div>
            </div>
          </CardContainer>

          {/* 系统信息 */}
          <CardContainer title="系统信息">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-sm mb-2">技术栈</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Next.js 15</li>
                  <li>• TypeScript</li>
                  <li>• Tailwind CSS v4</li>
                  <li>• shadcn/ui</li>
                  <li>• MySQL 8.0</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-sm mb-2">功能模块</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• 竞品管理</li>
                  <li>• 票种管理</li>
                  <li>• 促销活动管理</li>
                  <li>• 数据统计分析</li>
                  <li>• 响应式设计</li>
                </ul>
              </div>
            </div>
          </CardContainer>
        </div>
      </PageContainer>
    </MainLayout>
  );
}
