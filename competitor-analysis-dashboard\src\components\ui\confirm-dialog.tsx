/**
 * 确认对话框组件
 * 
 * 功能说明：
 * 1. 提供通用的确认对话框
 * 2. 支持不同类型的确认操作
 * 3. 可配置的标题、内容和按钮
 * 4. 支持异步操作和加载状态
 */

'use client';

import React, { useState } from 'react';
import { AlertTriangle, Trash2, Edit, Eye, Plus } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { LoadingButton } from '@/components/ui/loading';
import { Badge } from '@/components/ui/badge';

// ============================================================================
// 确认对话框类型定义
// ============================================================================

export type ConfirmType = 'delete' | 'edit' | 'create' | 'view' | 'warning' | 'info';

export interface ConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type?: ConfirmType;
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  children?: React.ReactNode;
  data?: any; // 可以传递额外的数据用于显示
}

// ============================================================================
// 确认对话框组件实现
// ============================================================================

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  open,
  onOpenChange,
  type = 'warning',
  title,
  description,
  confirmText,
  cancelText = '取消',
  onConfirm,
  onCancel,
  loading = false,
  children,
  data
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  // 根据类型获取默认配置
  const getTypeConfig = () => {
    switch (type) {
      case 'delete':
        return {
          icon: Trash2,
          iconColor: 'text-red-500',
          title: title || '确认删除',
          description: description || '此操作不可撤销，确定要删除吗？',
          confirmText: confirmText || '删除',
          confirmVariant: 'destructive' as const
        };
      case 'edit':
        return {
          icon: Edit,
          iconColor: 'text-blue-500',
          title: title || '确认编辑',
          description: description || '确定要编辑此项吗？',
          confirmText: confirmText || '编辑',
          confirmVariant: 'default' as const
        };
      case 'create':
        return {
          icon: Plus,
          iconColor: 'text-green-500',
          title: title || '确认创建',
          description: description || '确定要创建此项吗？',
          confirmText: confirmText || '创建',
          confirmVariant: 'default' as const
        };
      case 'view':
        return {
          icon: Eye,
          iconColor: 'text-gray-500',
          title: title || '查看详情',
          description: description || '',
          confirmText: confirmText || '确定',
          confirmVariant: 'default' as const
        };
      case 'warning':
        return {
          icon: AlertTriangle,
          iconColor: 'text-yellow-500',
          title: title || '警告',
          description: description || '请确认您的操作',
          confirmText: confirmText || '确认',
          confirmVariant: 'default' as const
        };
      case 'info':
      default:
        return {
          icon: AlertTriangle,
          iconColor: 'text-blue-500',
          title: title || '提示',
          description: description || '请确认您的操作',
          confirmText: confirmText || '确认',
          confirmVariant: 'default' as const
        };
    }
  };

  const config = getTypeConfig();
  const Icon = config.icon;

  // 处理确认操作
  const handleConfirm = async () => {
    if (!onConfirm) return;

    try {
      setIsProcessing(true);
      await onConfirm();
      onOpenChange(false);
    } catch (error) {
      console.error('确认操作失败:', error);
      // 这里可以添加错误提示
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理取消操作
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full bg-gray-100 ${config.iconColor}`}>
              <Icon className="h-5 w-5" />
            </div>
            <div>
              <DialogTitle className="text-lg font-semibold">
                {config.title}
              </DialogTitle>
            </div>
          </div>
        </DialogHeader>

        <div className="py-4">
          {config.description && (
            <DialogDescription className="text-gray-600 mb-4">
              {config.description}
            </DialogDescription>
          )}

          {/* 自定义内容 */}
          {children}

          {/* 数据展示 */}
          {data && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium mb-2">相关信息：</h4>
              <div className="space-y-2">
                {Object.entries(data).map(([key, value]) => (
                  <div key={key} className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">{key}:</span>
                    <span className="font-medium">
                      {typeof value === 'boolean' ? (
                        <Badge variant={value ? 'default' : 'secondary'}>
                          {value ? '是' : '否'}
                        </Badge>
                      ) : (
                        value?.toString() || '-'
                      )}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isProcessing || loading}
          >
            {cancelText}
          </Button>
          
          {onConfirm && (
            <LoadingButton
              variant={config.confirmVariant}
              onClick={handleConfirm}
              loading={isProcessing || loading}
              loadingText="处理中..."
            >
              {config.confirmText}
            </LoadingButton>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// ============================================================================
// 删除确认对话框（特化组件）
// ============================================================================

export interface DeleteConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  itemName?: string;
  itemType?: string;
  onConfirm: () => void | Promise<void>;
  loading?: boolean;
  additionalInfo?: Record<string, any>;
}

export const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  open,
  onOpenChange,
  title,
  itemName,
  itemType = '项目',
  onConfirm,
  loading = false,
  additionalInfo
}) => {
  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      type="delete"
      title={title || `删除${itemType}`}
      description={
        itemName 
          ? `确定要删除${itemType}"${itemName}"吗？此操作不可撤销。`
          : `确定要删除此${itemType}吗？此操作不可撤销。`
      }
      confirmText="删除"
      onConfirm={onConfirm}
      loading={loading}
      data={additionalInfo}
    />
  );
};

// ============================================================================
// 批量操作确认对话框
// ============================================================================

export interface BatchConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  action: string;
  itemCount: number;
  itemType?: string;
  onConfirm: () => void | Promise<void>;
  loading?: boolean;
}

export const BatchConfirmDialog: React.FC<BatchConfirmDialogProps> = ({
  open,
  onOpenChange,
  action,
  itemCount,
  itemType = '项目',
  onConfirm,
  loading = false
}) => {
  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      type={action === '删除' ? 'delete' : 'warning'}
      title={`批量${action}`}
      description={`确定要${action}选中的 ${itemCount} 个${itemType}吗？`}
      confirmText={action}
      onConfirm={onConfirm}
      loading={loading}
    />
  );
};

// ============================================================================
// 导出所有组件
// ============================================================================

export {
  ConfirmDialog as default,
  DeleteConfirmDialog,
  BatchConfirmDialog
};
