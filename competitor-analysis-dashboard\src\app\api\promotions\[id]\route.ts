/**
 * 单个促销活动API路由
 * 
 * 功能说明：
 * 1. 处理单个促销活动的查询、更新、删除操作
 * 2. 提供数据验证和错误处理
 * 3. 支持部分更新（PATCH）
 */

import { NextRequest } from 'next/server';
import { promotionDAO, competitorDAO, ticketTypeDAO } from '@/lib/dao';
import {
  createSuccessResponse,
  createNotFoundResponse,
  createValidationErrorResponse,
  parseRequestBody,
  validateRequiredFields,
  validateStringLength,
  validateNumberRange,
  validateDateFormat,
  validateUrlFormat,
  withErrorHandling,
  createMethodNotAllowedResponse
} from '@/lib/api-utils';
import { UpdatePromotionInput } from '@/types';

/**
 * GET /api/promotions/[id]
 * 获取单个促销活动详情
 */
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const promotionId = parseInt(params.id);
  console.log(`📥 GET /api/promotions/${promotionId} - 获取促销活动详情`);

  // 验证ID格式
  if (isNaN(promotionId)) {
    return createValidationErrorResponse('无效的促销活动ID');
  }

  // 查询促销活动详情
  const promotion = await promotionDAO.findDetailById(promotionId);
  if (!promotion) {
    return createNotFoundResponse('促销活动', promotionId);
  }

  console.log(`✅ 成功获取促销活动详情: ${promotion.activity_name}`);
  return createSuccessResponse(promotion, '获取促销活动详情成功');
});

/**
 * PUT /api/promotions/[id]
 * 完整更新促销活动信息
 */
export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const promotionId = parseInt(params.id);
  console.log(`📥 PUT /api/promotions/${promotionId} - 完整更新促销活动`);

  // 验证ID格式
  if (isNaN(promotionId)) {
    return createValidationErrorResponse('无效的促销活动ID');
  }

  // 检查促销活动是否存在
  const existingPromotion = await promotionDAO.findById(promotionId);
  if (!existingPromotion) {
    return createNotFoundResponse('促销活动', promotionId);
  }

  // 解析请求体
  const body = await parseRequestBody<UpdatePromotionInput>(request);
  console.log('📝 更新数据:', body);

  // 验证外键关联（如果提供了新的ID）
  if (body.competitor_id !== undefined) {
    const competitor = await competitorDAO.findById(body.competitor_id);
    if (!competitor) {
      return createValidationErrorResponse('指定的竞品不存在');
    }
  }

  if (body.ticket_type_id !== undefined) {
    const ticketType = await ticketTypeDAO.findById(body.ticket_type_id);
    if (!ticketType) {
      return createValidationErrorResponse('指定的票种不存在');
    }
  }

  // 验证字段格式和长度
  if (body.activity_name !== undefined && !validateStringLength(body.activity_name, 1, 255)) {
    return createValidationErrorResponse('活动名称长度必须在1-255个字符之间');
  }

  if (body.rack_rate !== undefined && body.rack_rate !== null && !validateNumberRange(body.rack_rate, 0, 999999.99)) {
    return createValidationErrorResponse('门市价必须在0-999999.99之间');
  }

  if (body.promo_price !== undefined && body.promo_price !== null && !validateNumberRange(body.promo_price, 0, 999999.99)) {
    return createValidationErrorResponse('促销价必须在0-999999.99之间');
  }

  // 验证日期格式
  if (body.sale_start_date && !validateDateFormat(body.sale_start_date)) {
    return createValidationErrorResponse('销售开始日期格式无效');
  }

  if (body.sale_end_date && !validateDateFormat(body.sale_end_date)) {
    return createValidationErrorResponse('销售结束日期格式无效');
  }

  if (body.use_start_date && !validateDateFormat(body.use_start_date)) {
    return createValidationErrorResponse('使用开始日期格式无效');
  }

  if (body.use_end_date && !validateDateFormat(body.use_end_date)) {
    return createValidationErrorResponse('使用结束日期格式无效');
  }

  // 验证其他字段
  if (body.sales_channel && !validateStringLength(body.sales_channel, 0, 255)) {
    return createValidationErrorResponse('销售渠道长度不能超过255个字符');
  }

  if (body.usage_rules && !validateStringLength(body.usage_rules, 0, 1000)) {
    return createValidationErrorResponse('使用规则长度不能超过1000个字符');
  }

  if (body.data_source_url && !validateUrlFormat(body.data_source_url)) {
    return createValidationErrorResponse('数据来源URL格式无效');
  }

  if (body.data_source_url && !validateStringLength(body.data_source_url, 0, 512)) {
    return createValidationErrorResponse('数据来源URL长度不能超过512个字符');
  }

  if (body.remarks && !validateStringLength(body.remarks, 0, 500)) {
    return createValidationErrorResponse('备注长度不能超过500个字符');
  }

  // 更新促销活动
  const updateSuccess = await promotionDAO.update(promotionId, body);
  if (!updateSuccess) {
    return createValidationErrorResponse('更新失败，可能是数据未发生变化');
  }

  // 获取更新后的促销活动详情
  const updatedPromotion = await promotionDAO.findDetailById(promotionId);

  console.log(`✅ 成功更新促销活动: ${updatedPromotion?.activity_name}`);
  return createSuccessResponse(updatedPromotion, '更新促销活动成功');
});

/**
 * PATCH /api/promotions/[id]
 * 部分更新促销活动信息
 */
export const PATCH = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const promotionId = parseInt(params.id);
  console.log(`📥 PATCH /api/promotions/${promotionId} - 部分更新促销活动`);

  // 验证ID格式
  if (isNaN(promotionId)) {
    return createValidationErrorResponse('无效的促销活动ID');
  }

  // 检查促销活动是否存在
  const existingPromotion = await promotionDAO.findById(promotionId);
  if (!existingPromotion) {
    return createNotFoundResponse('促销活动', promotionId);
  }

  // 解析请求体
  const body = await parseRequestBody<UpdatePromotionInput>(request);
  console.log('📝 部分更新数据:', body);

  // 验证外键关联（如果提供了新的ID）
  if (body.competitor_id !== undefined) {
    const competitor = await competitorDAO.findById(body.competitor_id);
    if (!competitor) {
      return createValidationErrorResponse('指定的竞品不存在');
    }
  }

  if (body.ticket_type_id !== undefined) {
    const ticketType = await ticketTypeDAO.findById(body.ticket_type_id);
    if (!ticketType) {
      return createValidationErrorResponse('指定的票种不存在');
    }
  }

  // 验证字段格式和长度（只验证提供的字段）
  if (body.activity_name !== undefined && !validateStringLength(body.activity_name, 1, 255)) {
    return createValidationErrorResponse('活动名称长度必须在1-255个字符之间');
  }

  if (body.rack_rate !== undefined && body.rack_rate !== null && !validateNumberRange(body.rack_rate, 0, 999999.99)) {
    return createValidationErrorResponse('门市价必须在0-999999.99之间');
  }

  if (body.promo_price !== undefined && body.promo_price !== null && !validateNumberRange(body.promo_price, 0, 999999.99)) {
    return createValidationErrorResponse('促销价必须在0-999999.99之间');
  }

  // 验证日期格式
  if (body.sale_start_date && !validateDateFormat(body.sale_start_date)) {
    return createValidationErrorResponse('销售开始日期格式无效');
  }

  if (body.sale_end_date && !validateDateFormat(body.sale_end_date)) {
    return createValidationErrorResponse('销售结束日期格式无效');
  }

  if (body.use_start_date && !validateDateFormat(body.use_start_date)) {
    return createValidationErrorResponse('使用开始日期格式无效');
  }

  if (body.use_end_date && !validateDateFormat(body.use_end_date)) {
    return createValidationErrorResponse('使用结束日期格式无效');
  }

  // 验证其他字段
  if (body.sales_channel && !validateStringLength(body.sales_channel, 0, 255)) {
    return createValidationErrorResponse('销售渠道长度不能超过255个字符');
  }

  if (body.usage_rules && !validateStringLength(body.usage_rules, 0, 1000)) {
    return createValidationErrorResponse('使用规则长度不能超过1000个字符');
  }

  if (body.data_source_url && !validateUrlFormat(body.data_source_url)) {
    return createValidationErrorResponse('数据来源URL格式无效');
  }

  if (body.data_source_url && !validateStringLength(body.data_source_url, 0, 512)) {
    return createValidationErrorResponse('数据来源URL长度不能超过512个字符');
  }

  if (body.remarks && !validateStringLength(body.remarks, 0, 500)) {
    return createValidationErrorResponse('备注长度不能超过500个字符');
  }

  // 更新促销活动
  const updateSuccess = await promotionDAO.update(promotionId, body);
  if (!updateSuccess) {
    return createValidationErrorResponse('更新失败，可能是数据未发生变化');
  }

  // 获取更新后的促销活动详情
  const updatedPromotion = await promotionDAO.findDetailById(promotionId);

  console.log(`✅ 成功部分更新促销活动: ${updatedPromotion?.activity_name}`);
  return createSuccessResponse(updatedPromotion, '部分更新促销活动成功');
});

/**
 * DELETE /api/promotions/[id]
 * 删除促销活动
 */
export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const promotionId = parseInt(params.id);
  console.log(`📥 DELETE /api/promotions/${promotionId} - 删除促销活动`);

  // 验证ID格式
  if (isNaN(promotionId)) {
    return createValidationErrorResponse('无效的促销活动ID');
  }

  // 检查促销活动是否存在
  const existingPromotion = await promotionDAO.findDetailById(promotionId);
  if (!existingPromotion) {
    return createNotFoundResponse('促销活动', promotionId);
  }

  // 删除促销活动
  const deleteSuccess = await promotionDAO.delete(promotionId);
  if (!deleteSuccess) {
    return createValidationErrorResponse('删除失败');
  }

  console.log(`✅ 成功删除促销活动: ${existingPromotion.activity_name}`);
  return createSuccessResponse(
    { deleted: true, promotion: existingPromotion },
    '删除促销活动成功'
  );
});

/**
 * 处理不支持的HTTP方法
 */
export async function POST(request: NextRequest) {
  return createMethodNotAllowedResponse(['GET', 'PUT', 'PATCH', 'DELETE']);
}
