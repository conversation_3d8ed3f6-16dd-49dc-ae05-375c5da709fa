/**
 * 基础数据访问对象(DAO)类
 * 
 * 功能说明：
 * 1. 提供通用的CRUD操作方法
 * 2. 实现分页查询和排序功能
 * 3. 提供事务支持和错误处理
 * 4. 作为其他DAO类的基类
 */

import { executeQuery, executeTransaction } from '../database';
import { PaginationParams, PaginatedResult } from '@/types';
import mysql from 'mysql2/promise';

/**
 * 基础DAO抽象类
 * 提供通用的数据库操作方法
 */
export abstract class BaseDAO<T, CreateInput, UpdateInput> {
  protected tableName: string;
  protected primaryKey: string;

  constructor(tableName: string, primaryKey: string = 'id') {
    this.tableName = tableName;
    this.primaryKey = primaryKey;
  }

  /**
   * 根据ID查询单条记录
   * @param id 记录ID
   * @returns 查询结果
   */
  async findById(id: string | number): Promise<T | null> {
    try {
      console.log(`🔍 查询${this.tableName}表中ID为${id}的记录`);
      
      const query = `SELECT * FROM ${this.tableName} WHERE ${this.primaryKey} = ?`;
      const results = await executeQuery<T>(query, [id]);
      
      if (results.length === 0) {
        console.log(`⚠️ 未找到ID为${id}的记录`);
        return null;
      }
      
      console.log(`✅ 成功查询到记录`);
      return results[0];
    } catch (error) {
      console.error(`❌ 查询${this.tableName}记录失败:`, error);
      throw new Error(`查询记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 查询所有记录
   * @param orderBy 排序字段
   * @param orderDirection 排序方向
   * @returns 查询结果列表
   */
  async findAll(
    orderBy: string = this.primaryKey,
    orderDirection: 'ASC' | 'DESC' = 'ASC'
  ): Promise<T[]> {
    try {
      console.log(`🔍 查询${this.tableName}表的所有记录`);
      
      const query = `SELECT * FROM ${this.tableName} ORDER BY ${orderBy} ${orderDirection}`;
      const results = await executeQuery<T>(query);
      
      console.log(`✅ 成功查询到${results.length}条记录`);
      return results;
    } catch (error) {
      console.error(`❌ 查询${this.tableName}所有记录失败:`, error);
      throw new Error(`查询记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 分页查询记录
   * @param params 分页参数
   * @param whereClause WHERE子句
   * @param whereParams WHERE参数
   * @returns 分页查询结果
   */
  async findWithPagination(
    params: PaginationParams,
    whereClause: string = '',
    whereParams: any[] = []
  ): Promise<PaginatedResult<T>> {
    try {
      const { page, pageSize, sortBy = this.primaryKey, sortOrder = 'ASC' } = params;
      const offset = (page - 1) * pageSize;
      
      console.log(`🔍 分页查询${this.tableName}表 - 第${page}页，每页${pageSize}条`);
      
      // 构建WHERE子句
      const whereSQL = whereClause ? `WHERE ${whereClause}` : '';
      
      // 查询总数
      const countQuery = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereSQL}`;
      const countResults = await executeQuery<{ total: number }>(countQuery, whereParams);
      const total = countResults[0].total;
      
      // 查询数据 - 使用字符串拼接避免参数类型问题
      const dataQuery = `
        SELECT * FROM ${this.tableName}
        ${whereSQL}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT ${Number(pageSize)} OFFSET ${Number(offset)}
      `;
      const dataResults = await executeQuery<T>(dataQuery, whereParams);
      
      const totalPages = Math.ceil(total / pageSize);
      
      console.log(`✅ 分页查询成功 - 总计${total}条记录，当前页${dataResults.length}条`);
      
      return {
        data: dataResults,
        total,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      console.error(`❌ 分页查询${this.tableName}失败:`, error);
      throw new Error(`分页查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 创建新记录
   * @param data 创建数据
   * @returns 创建的记录ID
   */
  async create(data: CreateInput): Promise<number> {
    try {
      console.log(`➕ 创建${this.tableName}新记录`);
      console.log('📝 创建数据:', data);
      
      // 过滤掉值为 undefined 的字段，避免将其写入为 NULL 或覆盖默认值
      const entries = Object.entries(data as any).filter(([, v]) => v !== undefined);
      const fields = entries.map(([k]) => k);
      const values = entries.map(([, v]) => v);
      const placeholders = fields.map(() => '?').join(', ');
      
      const query = `
        INSERT INTO ${this.tableName} (${fields.join(', ')}) 
        VALUES (${placeholders})
      `;
      
      const results = await executeQuery(query, values);
      const insertId = (results as any).insertId;
      
      console.log(`✅ 成功创建记录，ID: ${insertId}`);
      return insertId;
    } catch (error) {
      console.error(`❌ 创建${this.tableName}记录失败:`, error);
      throw new Error(`创建记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 更新记录
   * @param id 记录ID
   * @param data 更新数据
   * @returns 是否更新成功
   */
  async update(id: string | number, data: UpdateInput): Promise<boolean> {
    try {
      console.log(`✏️ 更新${this.tableName}记录，ID: ${id}`);
      console.log('📝 更新数据:', data);
      
      // 过滤掉值为 undefined 的字段
      const entries = Object.entries(data as any).filter(([, v]) => v !== undefined);
      const fields = entries.map(([k]) => k);
      const values = entries.map(([, v]) => v);
      
      if (fields.length === 0) {
        console.log('⚠️ 没有需要更新的字段');
        return false;
      }
      
      const setClause = fields.map(field => `${field} = ?`).join(', ');
      const query = `UPDATE ${this.tableName} SET ${setClause} WHERE ${this.primaryKey} = ?`;
      
      const results = await executeQuery(query, [...values, id]);
      const affectedRows = (results as any).affectedRows;
      
      if (affectedRows === 0) {
        console.log(`⚠️ 未找到ID为${id}的记录或数据未发生变化`);
        return false;
      }
      
      console.log(`✅ 成功更新记录，影响行数: ${affectedRows}`);
      return true;
    } catch (error) {
      console.error(`❌ 更新${this.tableName}记录失败:`, error);
      throw new Error(`更新记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 删除记录
   * @param id 记录ID
   * @returns 是否删除成功
   */
  async delete(id: string | number): Promise<boolean> {
    try {
      console.log(`🗑️ 删除${this.tableName}记录，ID: ${id}`);
      
      const query = `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} = ?`;
      const results = await executeQuery(query, [id]);
      const affectedRows = (results as any).affectedRows;
      
      if (affectedRows === 0) {
        console.log(`⚠️ 未找到ID为${id}的记录`);
        return false;
      }
      
      console.log(`✅ 成功删除记录，影响行数: ${affectedRows}`);
      return true;
    } catch (error) {
      console.error(`❌ 删除${this.tableName}记录失败:`, error);
      throw new Error(`删除记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 批量删除记录
   * @param ids 记录ID列表
   * @returns 删除的记录数量
   */
  async batchDelete(ids: (string | number)[]): Promise<number> {
    try {
      if (ids.length === 0) {
        console.log('⚠️ 没有需要删除的记录');
        return 0;
      }
      
      console.log(`🗑️ 批量删除${this.tableName}记录，数量: ${ids.length}`);
      
      const placeholders = ids.map(() => '?').join(', ');
      const query = `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} IN (${placeholders})`;
      
      const results = await executeQuery(query, ids);
      const affectedRows = (results as any).affectedRows;
      
      console.log(`✅ 成功删除${affectedRows}条记录`);
      return affectedRows;
    } catch (error) {
      console.error(`❌ 批量删除${this.tableName}记录失败:`, error);
      throw new Error(`批量删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 检查记录是否存在
   * @param id 记录ID
   * @returns 是否存在
   */
  async exists(id: string | number): Promise<boolean> {
    try {
      const query = `SELECT 1 FROM ${this.tableName} WHERE ${this.primaryKey} = ? LIMIT 1`;
      const results = await executeQuery(query, [id]);
      return results.length > 0;
    } catch (error) {
      console.error(`❌ 检查${this.tableName}记录存在性失败:`, error);
      throw new Error(`检查记录存在性失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取记录总数
   * @param whereClause WHERE子句
   * @param whereParams WHERE参数
   * @returns 记录总数
   */
  async count(whereClause: string = '', whereParams: any[] = []): Promise<number> {
    try {
      const whereSQL = whereClause ? `WHERE ${whereClause}` : '';
      const query = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereSQL}`;
      
      const results = await executeQuery<{ total: number }>(query, whereParams);
      return results[0].total;
    } catch (error) {
      console.error(`❌ 统计${this.tableName}记录数量失败:`, error);
      throw new Error(`统计记录数量失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 执行事务操作
   * @param callback 事务回调函数
   * @returns 事务执行结果
   */
  async executeInTransaction<R>(
    callback: (connection: mysql.PoolConnection) => Promise<R>
  ): Promise<R> {
    return executeTransaction(callback);
  }
}
